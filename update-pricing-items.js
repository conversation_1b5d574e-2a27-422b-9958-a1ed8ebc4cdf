// Script to update pricing items to match the UI
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Define the pricing items that should match the UI
const pricingItems = [
  {
    service: 'Business Card Design',
    price: 3000,
    description: 'Professional business card design for your brand',
    features: [
      '3 unique concepts',
      '2 revision rounds',
      'Print-ready files',
      'Digital formats for online use',
      'Double-sided design',
      'Delivery in 2 days'
    ],
    icon: 'id-card',
    popular: false
  },
  {
    service: 'Flyer Design',
    price: 4500,
    description: 'Eye-catching flyer designs for your marketing campaigns',
    features: [
      '2 design concepts',
      '3 revision rounds',
      'Print-ready files (CMYK)',
      'Web formats (RGB)',
      'Custom illustrations',
      'Delivery in 3 days'
    ],
    icon: 'file-image',
    popular: false
  },
  {
    service: 'Brochure Design',
    price: 8000,
    description: 'Professional multi-page brochure design',
    features: [
      'Up to 8-page design',
      '3 revision rounds',
      'Print-ready files',
      'Digital formats',
      'Custom graphics',
      'Layout & typography',
      'Delivery in 5 days'
    ],
    icon: 'newspaper',
    popular: true
  }
];

async function main() {
  console.log('Updating pricing items to match the UI...');

  try {
    // Get existing pricing items
    const existingItems = await prisma.pricing.findMany();
    console.log(`Found ${existingItems.length} existing pricing items`);

    // Create a map of existing items by service name (case insensitive)
    const existingItemsMap = new Map();
    existingItems.forEach(item => {
      existingItemsMap.set(item.service.toLowerCase(), item);
    });

    // Process each pricing item
    for (const item of pricingItems) {
      const existingItem = existingItemsMap.get(item.service.toLowerCase());

      if (existingItem) {
        // Update existing item
        console.log(`Updating existing item: ${item.service}`);
        await prisma.pricing.update({
          where: { id: existingItem.id },
          data: {
            price: item.price,
            description: item.description,
            features: item.features,
            icon: item.icon,
            popular: item.popular
          }
        });
      } else {
        // Create new item
        console.log(`Creating new item: ${item.service}`);
        await prisma.pricing.create({
          data: {
            service: item.service,
            price: item.price,
            description: item.description,
            features: item.features,
            icon: item.icon,
            popular: item.popular
          }
        });
      }
    }

    console.log('Pricing items updated successfully');

    // Verify the updated items
    const updatedItems = await prisma.pricing.findMany();
    console.log('Updated pricing items:');
    updatedItems.forEach(item => {
      console.log(`- ${item.service}: KSh ${item.price} (${item.popular ? 'Popular' : 'Standard'})`);
    });
  } catch (error) {
    console.error('Error updating pricing items:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch(e => {
    console.error('Error:', e);
    process.exit(1);
  });
