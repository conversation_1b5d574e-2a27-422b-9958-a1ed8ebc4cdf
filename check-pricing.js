// Script to check pricing data
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    const pricing = await prisma.pricing.findMany();
    console.log('Current pricing items:');
    console.log(JSON.stringify(pricing, null, 2));
    console.log(`Total pricing items: ${pricing.length}`);
  } catch (error) {
    console.error('Error fetching pricing data:', error);
  }
}

main()
  .catch(e => {
    console.error('Error:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 