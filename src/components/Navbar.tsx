'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';

// Reorganized navigation links with improved structure based on existing pages only
const navLinks = [
  { href: '/', label: 'Home' },
  { href: '/about', label: 'About Us' },
  { href: '/blog', label: 'Blog' },
  { href: '/pricing', label: 'Catalogue' },
  {
    label: 'Services',
    dropdown: true,
    items: [
      {
        href: '/services',
        label: 'All Services',
        icon: 'far fa-list-alt',
        description: 'Browse our complete range of services'
      },
      {
        label: 'Design Services',
        isGroup: true,
        items: [
          {
            href: '/logos',
            label: 'Logo Design',
            icon: 'fas fa-vector-square',
            description: 'Professional logo design services'
          },
          {
            href: '/graphics',
            label: 'Graphic Design',
            icon: 'fas fa-paint-brush',
            description: 'Stunning graphics for all your needs'
          }
        ]
      },
      {
        label: 'Digital Services',
        isGroup: true,
        items: [
          {
            href: '/web-development',
            label: 'Web Development',
            icon: 'fas fa-code',
            description: 'Custom website development'
          },
          {
            href: '/social-media',
            label: 'Social Media',
            icon: 'fas fa-hashtag',
            description: 'Social media strategy and management'
          }
        ]
      }
    ]
  },
  {
    href: '/portfolio',
    label: 'Portfolio'
  },
  {
    href: "/contact",
    label: "Contact"
  }
];

// CSS for animations
const animationCSS = `
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideDown {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease forwards;
}

.animate-slide-down {
  animation: slideDown 0.3s ease forwards;
}
`;

export default function Navbar() {
  const [mounted, setMounted] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [mobileDropdown, setMobileDropdown] = useState<string | null>(null);
  const [mobileSubDropdown, setMobileSubDropdown] = useState<string | null>(null);
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);
  const [scrolled, setScrolled] = useState(false);
  const headerRef = useRef<HTMLElement>(null);
  const menuRef = useRef<HTMLDivElement>(null);

  // Handle component mount
  useEffect(() => {
    setMounted(true);
  }, []);

  // Handle scroll events
  useEffect(() => {
    if (!mounted) return;

    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      setScrolled(isScrolled);

      if (headerRef.current) {
        const header = headerRef.current;
        header.style.padding = isScrolled ? '0.5rem 0' : '1.5rem 0';
        header.style.boxShadow = isScrolled
          ? '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          : '0 1px 3px rgba(0, 0, 0, 0.1)';
      }
    };

    const handleClickOutside = (e: MouseEvent) => {
      if (openDropdown && !((e.target as Element).closest('.dropdown-toggle'))) {
        setOpenDropdown(null);
      }

      if (isOpen && menuRef.current && !menuRef.current.contains(e.target as Node) &&
          !(e.target as Element).closest('.mobile-toggle')) {
        setIsOpen(false);
      }
    };

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setOpenDropdown(null);
        if (isOpen) {
          setIsOpen(false);
        }
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    document.addEventListener('click', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);

    handleScroll();

    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      window.removeEventListener('scroll', handleScroll);
      document.removeEventListener('click', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = '';
    };
  }, [openDropdown, isOpen, mounted]);

  // Component mount effect - add CSS animation styles
  useEffect(() => {
    // Add CSS animations to the document head
    if (typeof document !== 'undefined') {
      const style = document.createElement('style');
      style.innerHTML = animationCSS;
      style.id = 'navbar-animations';

      // Only add if not already present
      if (!document.getElementById('navbar-animations')) {
        document.head.appendChild(style);
      }

      return () => {
        // Clean up on unmount
        const existingStyle = document.getElementById('navbar-animations');
        if (existingStyle) {
          existingStyle.remove();
        }
      };
    }
  }, []);

  // Toggle desktop dropdown menu
  const toggleDropdown = (label: string) => {
    if (openDropdown === label) {
      setOpenDropdown(null);
    } else {
      setOpenDropdown(label);
    }
  };

  // Toggle mobile dropdown
  const toggleMobileDropdown = (label: string) => {
    if (mobileDropdown === label) {
      setMobileDropdown(null);
    } else {
      setMobileDropdown(label);
    }
    setMobileSubDropdown(null);
  };

  // Toggle mobile sub-dropdown
  const toggleMobileSubDropdown = (label: string) => {
    if (mobileSubDropdown === label) {
      setMobileSubDropdown(null);
    } else {
      setMobileSubDropdown(label);
    }
  };

  // Toggle mobile menu
  const toggleMobileMenu = () => {
    setIsOpen(!isOpen);
    if (isOpen) {
      setMobileDropdown(null);
      setMobileSubDropdown(null);
    }
  };

  // Return null during SSR
  if (!mounted) {
    return null;
  }

  return (
    <>
      <header
        ref={headerRef}
        className="fixed top-0 left-0 right-0 z-40 bg-white shadow py-6"
        style={{
          transition: 'padding 0.3s ease, box-shadow 0.3s ease',
          padding: scrolled ? '0.5rem 0' : '1.5rem 0',
          boxShadow: scrolled ? '0 4px 6px -1px rgba(0, 0, 0, 0.1)' : '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}
      >
        <div className="container mx-auto px-4 max-w-7xl">
          <div className="flex justify-between items-center">
            {/* Logo */}
            <Link href="/" className="flex items-center">
              <Image
                src="/images/logo.png"
                alt="Mocky Digital Logo"
                width={36}
                height={36}
                className="mr-2"
                priority
              />
              <span className="self-center text-lg font-semibold whitespace-nowrap text-gray-900">
                Mocky Digital
              </span>
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-1">
              {navLinks.map((link) =>
                link.dropdown ? (
                  <div key={link.label} className="relative dropdown-toggle group">
                    <button
                      className={`font-medium px-3 py-2 rounded-md flex items-center transition-all duration-200 ${
                        openDropdown === link.label
                          ? 'text-primary bg-gray-50'
                          : 'text-gray-800 hover:text-primary group-hover:bg-gray-50'
                      }`}
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleDropdown(link.label);
                      }}
                      aria-expanded={openDropdown === link.label}
                    >
                      {link.label}
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className={`h-4 w-4 ml-1 transition-transform duration-200 ${openDropdown === link.label ? 'rotate-180 text-primary' : ''}`}
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>

                    {openDropdown === link.label && (
                      <div className={`absolute top-full left-0 mt-2 ${link.label === 'Services' ? 'w-[600px] -left-40' : 'w-72'} bg-white shadow-xl rounded-lg py-4 z-50 animate-slide-down border border-gray-100`}>
                        {link.label === 'Services' ? (
                          <div className="grid grid-cols-2 gap-4 p-4">
                            {/* All Services Link */}
                            <div className="col-span-2 mb-2">
                              <Link
                                href="/services"
                                className="flex items-center p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors group"
                                onClick={() => setOpenDropdown(null)}
                              >
                                <span className="mr-3 text-primary group-hover:text-primary-dark transition-colors">
                                  <i className="far fa-list-alt text-lg"></i>
                                </span>
                                <div>
                                  <div className="font-medium text-gray-800 group-hover:text-gray-900">All Services</div>
                                  <div className="text-xs text-gray-500 group-hover:text-gray-600">Browse our complete range of services</div>
                                </div>
                              </Link>
                            </div>

                            {/* Service Categories */}
                            {link.items?.filter(item => item.isGroup).map((category) => (
                              <div key={category.label} className="p-3 rounded-lg hover:bg-gray-50">
                                <h3 className="text-sm font-bold text-primary mb-3 border-b border-gray-100 pb-2">{category.label}</h3>
                                <ul className="space-y-2">
                                  {category.items?.map((service) => (
                                    <li key={service.href}>
                                      <Link
                                        href={service.href || '#'}
                                        className="flex items-start group"
                                        onClick={() => setOpenDropdown(null)}
                                      >
                                        {service.icon && (
                                          <span className="mr-2 text-primary opacity-75 mt-0.5 group-hover:opacity-100 transition-opacity">
                                            <i className={`${service.icon} text-sm`}></i>
                                          </span>
                                        )}
                                        <div>
                                          <div className="text-sm font-medium text-gray-700 group-hover:text-primary transition-colors">{service.label}</div>
                                          {service.description && (
                                            <div className="text-xs text-gray-500 group-hover:text-gray-700 transition-colors">{service.description}</div>
                                          )}
                                        </div>
                                      </Link>
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            ))}
                          </div>
                        ) : (
                          // Standard dropdown for other menu items
                          <div className="py-2">
                            {link.items?.map((item) =>
                              item.isGroup ? (
                                <div key={item.label} className="py-2">
                                  <div className="px-4 text-xs font-semibold uppercase tracking-wider text-gray-500 pb-1">
                                    {item.label}
                                  </div>
                                  <div className="border-b border-gray-100 mb-1"></div>
                                  {item.items?.map((subItem) => (
                                    <Link
                                      key={subItem.href}
                                      href={subItem.href || '#'}
                                      className="flex items-start px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors"
                                      onClick={() => setOpenDropdown(null)}
                                    >
                                      {subItem.icon && (
                                        <span className="mr-3 text-primary opacity-75 mt-0.5">
                                          <i className={subItem.icon}></i>
                                        </span>
                                      )}
                                      <div>
                                        <div className="font-medium">{subItem.label}</div>
                                        {subItem.description && (
                                          <div className="text-xs text-gray-500">{subItem.description}</div>
                                        )}
                                      </div>
                                    </Link>
                                  ))}
                                </div>
                              ) : (
                                <Link
                                  key={item.href}
                                  href={item.href || '#'}
                                  className="flex items-start px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors"
                                  onClick={() => setOpenDropdown(null)}
                                >
                                  {item.icon && (
                                    <span className="mr-3 text-primary opacity-75 mt-0.5">
                                      <i className={item.icon}></i>
                                    </span>
                                  )}
                                  <div>
                                    <div className="font-medium">{item.label}</div>
                                    {item.description && (
                                      <div className="text-xs text-gray-500">{item.description}</div>
                                    )}
                                  </div>
                                </Link>
                              )
                            )}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                ) : (
                  <Link
                    key={link.href}
                    href={link.href || '#'}
                    className="font-medium text-gray-800 hover:text-primary transition-all duration-200 px-3 py-2 rounded-md hover:bg-gray-50 relative after:absolute after:bottom-1 after:left-1/2 after:-translate-x-1/2 after:w-0 after:h-0.5 after:bg-primary after:transition-all after:duration-200 hover:after:w-1/2"
                  >
                    {link.label}
                  </Link>
                )
              )}

              {/* Admin Button */}
              <Link
                href="/admin"
                className="ml-3 inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark transition-all duration-200 hover:shadow-md hover:-translate-y-0.5"
              >
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 4h6m0 0v6m0-6L10 14" />
                </svg>
                Admin
              </Link>
            </nav>

            {/* Mobile Menu Button */}
            <button
              className="lg:hidden mobile-toggle z-50 p-2 rounded-md focus:outline-none"
              onClick={toggleMobileMenu}
              aria-label="Toggle menu"
              aria-expanded={isOpen}
            >
              <div className="relative w-6 h-5">
                <span
                  className={`absolute w-6 h-0.5 bg-gray-800 transform transition-all duration-300 ease-in-out ${
                    isOpen ? 'rotate-45 top-2' : 'top-0'
                  }`}
                ></span>
                <span
                  className={`absolute w-6 h-0.5 bg-gray-800 top-2 transition-all duration-150 ease-in-out ${
                    isOpen ? 'opacity-0' : 'opacity-100'
                  }`}
                ></span>
                <span
                  className={`absolute w-6 h-0.5 bg-gray-800 transform transition-all duration-300 ease-in-out ${
                    isOpen ? '-rotate-45 top-2' : 'top-4'
                  }`}
                ></span>
              </div>
            </button>
          </div>
        </div>
      </header>

      {/* Mobile Menu */}
      <div
        className={`fixed inset-0 z-40 lg:hidden transition-all duration-300 ${
          isOpen ? 'bg-black/50 backdrop-blur-sm visible' : 'bg-black/0 invisible pointer-events-none'
        }`}
        aria-hidden={!isOpen}
        onClick={() => setIsOpen(false)}
      >
        <div
          ref={menuRef}
          className={`fixed top-0 right-0 h-full w-[320px] max-w-[85vw] bg-white shadow-xl z-50 overflow-y-auto transform transition-transform duration-300 ease-in-out ${
            isOpen ? 'translate-x-0' : 'translate-x-full'
          }`}
          onClick={(e) => e.stopPropagation()}
        >
          <div className="sticky top-0 bg-white z-10 border-b border-gray-100 p-4 flex items-center justify-between">
            <Link
              href="/"
              className="flex items-center space-x-2"
              onClick={() => setIsOpen(false)}
            >
              <Image
                src="/images/logo.png"
                alt="Mocky Digital Logo"
                width={28}
                height={28}
                priority
              />
              <span className="font-semibold text-gray-900">Mocky Digital</span>
            </Link>
            <button
              className="p-2 rounded-full hover:bg-gray-100 transition-colors focus:outline-none"
              onClick={() => setIsOpen(false)}
              aria-label="Close menu"
            >
              <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <nav className="py-3 px-4">
            {navLinks.map((link) => (
              <div key={link.label} className="mb-1 border-b border-gray-100 pb-1 last:border-0">
                {link.dropdown ? (
                  <div>
                    <button
                      className="flex items-center justify-between w-full p-3 text-left rounded-lg hover:bg-gray-50 focus:outline-none"
                      onClick={() => toggleMobileDropdown(link.label)}
                      aria-expanded={mobileDropdown === link.label}
                    >
                      <span className="font-medium text-gray-800">{link.label}</span>
                      <svg
                        className={`w-5 h-5 text-gray-500 transition-transform duration-200 ${
                          mobileDropdown === link.label ? 'rotate-180' : ''
                        }`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>

                    <div
                      className={`overflow-hidden transition-all duration-300 ease-in-out ${
                        mobileDropdown === link.label
                          ? 'max-h-[1000px] opacity-100'
                          : 'max-h-0 opacity-0'
                      }`}
                    >
                      <div className="mt-2 mb-3 space-y-2 px-2">
                        {/* Special treatment for Services dropdown */}
                        {link.label === 'Services' && (
                          <Link
                            href="/services"
                            className="flex items-center p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors mb-3"
                            onClick={() => setIsOpen(false)}
                          >
                            <span className="mr-3 text-primary">
                              <i className="far fa-list-alt text-lg"></i>
                            </span>
                            <div>
                              <div className="font-medium text-gray-800">All Services</div>
                              <div className="text-xs text-gray-500">Browse our complete range of services</div>
                            </div>
                          </Link>
                        )}

                        {link.items?.map((item) =>
                          item.isGroup ? (
                            <div key={item.label} className={`my-3 ${link.label === 'Services' ? 'bg-gray-50 rounded-lg p-2' : ''}`}>
                              <button
                                className={`flex items-center justify-between w-full p-2 text-left rounded-md focus:outline-none ${
                                  link.label === 'Services'
                                    ? 'border-b border-gray-200 pb-2'
                                    : 'bg-gray-50'
                                }`}
                                onClick={() => toggleMobileSubDropdown(item.label)}
                                aria-expanded={mobileSubDropdown === item.label}
                              >
                                <span className={`font-semibold ${link.label === 'Services' ? 'text-primary text-sm' : 'text-gray-700 text-sm'}`}>
                                  {item.label}
                                </span>
                                <svg
                                  className={`w-4 h-4 text-gray-500 transition-transform duration-200 ${
                                    mobileSubDropdown === item.label ? 'rotate-180' : ''
                                  }`}
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                </svg>
                              </button>

                              <div
                                className={`overflow-hidden transition-all duration-300 ease-in-out ${
                                  mobileSubDropdown === item.label
                                    ? 'max-h-96 opacity-100'
                                    : 'max-h-0 opacity-0'
                                }`}
                              >
                                <div className={`space-y-1 py-2 mt-1 ${link.label === 'Services' ? '' : 'ml-2 pl-2 border-l-2 border-primary/20'}`}>
                                  {item.items?.map((subItem) => (
                                    <Link
                                      key={subItem.href}
                                      href={subItem.href || '#'}
                                      className="flex items-start p-2 text-gray-600 hover:text-primary rounded-md transition-colors"
                                      onClick={() => setIsOpen(false)}
                                    >
                                      {subItem.icon && (
                                        <span className="mr-2 text-primary opacity-75 mt-0.5 text-sm">
                                          <i className={subItem.icon}></i>
                                        </span>
                                      )}
                                      <div>
                                        <div className="text-sm font-medium">{subItem.label}</div>
                                        {subItem.description && (
                                          <div className="text-xs text-gray-500">{subItem.description}</div>
                                        )}
                                      </div>
                                    </Link>
                                  ))}
                                </div>
                              </div>
                            </div>
                          ) : (
                            <Link
                              key={item.href}
                              href={item.href || '#'}
                              className="flex items-start p-3 text-gray-600 hover:text-primary rounded-md transition-colors"
                              onClick={() => setIsOpen(false)}
                            >
                              {item.icon && (
                                <span className="mr-3 text-primary opacity-75 mt-0.5 text-sm">
                                  <i className={item.icon}></i>
                                </span>
                              )}
                              <div>
                                <div className="text-sm font-medium">{item.label}</div>
                                {item.description && (
                                  <div className="text-xs text-gray-500">{item.description}</div>
                                )}
                              </div>
                            </Link>
                          )
                        )}
                      </div>
                    </div>
                  </div>
                ) : (
                  <Link
                    href={link.href || '#'}
                    className="flex items-center p-3 rounded-lg hover:bg-gray-50"
                    onClick={() => setIsOpen(false)}
                  >
                    <span className="font-medium text-gray-800">{link.label}</span>
                  </Link>
                )}
              </div>
            ))}

            {/* Mobile Admin Button */}
            <div className="mt-4">
              <Link
                href="/admin"
                className="flex items-center justify-center w-full px-4 py-3 rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark transition-all duration-200 hover:shadow-md"
                onClick={() => setIsOpen(false)}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 4h6m0 0v6m0-6L10 14" />
                </svg>
                Admin
              </Link>
            </div>
          </nav>
        </div>
      </div>
    </>
  );
}