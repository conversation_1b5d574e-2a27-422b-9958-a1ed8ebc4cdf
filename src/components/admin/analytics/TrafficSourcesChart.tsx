'use client';

import { useState, useEffect } from 'react';
import { ArrowPathIcon } from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';
import { Pie } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';
import type { TrafficSource } from '@/services/googleAnalyticsService';

// Register Chart.js components
ChartJS.register(ArcElement, Tooltip, Legend);

// Define colors for the chart
const chartColors = [
  'rgba(54, 162, 235, 0.8)',
  'rgba(255, 99, 132, 0.8)',
  'rgba(255, 206, 86, 0.8)',
  'rgba(75, 192, 192, 0.8)',
  'rgba(153, 102, 255, 0.8)',
  'rgba(255, 159, 64, 0.8)',
  'rgba(199, 199, 199, 0.8)',
  'rgba(83, 102, 255, 0.8)',
];

export default function TrafficSourcesChart() {
  const [trafficSources, setTrafficSources] = useState<TrafficSource[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchTrafficSources = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`${window.location.origin}/api/admin/analytics/traffic-sources`);

      if (!response.ok) {
        throw new Error('Failed to fetch traffic sources data');
      }

      const data = await response.json();
      setTrafficSources(data);
      setLastUpdated(new Date());
    } catch (err) {
      console.error('Error fetching traffic sources:', err);
      setError('Failed to load traffic sources data');
      toast.error('Failed to load traffic sources data');
    } finally {
      setIsLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchTrafficSources();
  }, []);

  // Handle refresh
  const handleRefresh = () => {
    fetchTrafficSources();
  };

  // Prepare chart data
  const chartData = {
    labels: trafficSources.map(source => source.source),
    datasets: [
      {
        data: trafficSources.map(source => source.sessions),
        backgroundColor: chartColors.slice(0, trafficSources.length),
        borderColor: chartColors.map(color => color.replace('0.8', '1')),
        borderWidth: 1,
      },
    ],
  };

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
        labels: {
          boxWidth: 15,
          padding: 15,
          font: {
            size: 12,
          },
        },
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const label = context.label || '';
            const value = context.raw || 0;
            const dataset = context.dataset;
            const total = dataset.data.reduce((acc: number, data: number) => acc + data, 0);
            const percentage = Math.round((value / total) * 100);
            return `${label}: ${value.toLocaleString()} sessions (${percentage}%)`;
          }
        }
      }
    },
  };

  // Render loading state
  if (isLoading && trafficSources.length === 0) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm border border-slate-100 animate-pulse">
        <div className="flex justify-between items-center mb-6">
          <div className="h-6 w-40 bg-slate-200 rounded"></div>
          <div className="h-8 w-8 bg-slate-200 rounded-full"></div>
        </div>
        <div className="h-64 bg-slate-200 rounded"></div>
      </div>
    );
  }

  // Render error state
  if (error && trafficSources.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6 text-center">
        <p className="text-red-500 mb-4">{error}</p>
        <button
          onClick={handleRefresh}
          className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-slate-100">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-slate-800">Traffic Sources</h2>
        <div className="flex items-center space-x-2">
          {trafficSources.length > 0 && (
            <div className="flex items-center">
              <span
                className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full mr-2 ${
                  trafficSources[0].isRealData
                    ? 'bg-green-100 text-green-800'
                    : 'bg-amber-100 text-amber-800'
                }`}
              >
                {trafficSources[0].isRealData ? 'Real Data' : 'Mock Data'}
              </span>
            </div>
          )}
          {lastUpdated && (
            <span className="text-xs text-slate-500">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </span>
          )}
          <button
            onClick={handleRefresh}
            disabled={isLoading}
            className="p-2 rounded-lg text-slate-500 hover:text-slate-700 hover:bg-slate-100 transition-colors"
            aria-label="Refresh traffic sources"
          >
            <ArrowPathIcon className={`h-5 w-5 ${isLoading ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      <div className="h-64 md:h-80">
        {trafficSources.length > 0 ? (
          <Pie data={chartData} options={chartOptions} />
        ) : (
          <div className="flex items-center justify-center h-full">
            <p className="text-slate-500">No traffic source data available</p>
          </div>
        )}
      </div>

      {trafficSources.length > 0 && (
        <div className="mt-6">
          <h3 className="text-sm font-medium text-slate-700 mb-3">Traffic Breakdown</h3>
          <div className="space-y-2">
            {trafficSources.map((source, index) => (
              <div key={source.source} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div
                    className="w-3 h-3 rounded-full mr-2"
                    style={{ backgroundColor: chartColors[index] }}
                  ></div>
                  <span className="text-sm text-slate-600">{source.source}</span>
                </div>
                <div className="text-sm font-medium text-slate-700">
                  {source.sessions.toLocaleString()} ({source.percentage}%)
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
