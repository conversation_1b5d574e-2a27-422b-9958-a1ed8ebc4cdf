'use client';

import { useState, useEffect } from 'react';
import {
  ChartBarIcon,
  UsersIcon,
  ArrowTrendingUpIcon,
  ClockIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';
import type { AnalyticsOverview } from '@/services/googleAnalyticsService';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  subtitle?: string;
}

const StatCard = ({ title, value, icon, subtitle }: StatCardProps) => (
  <div className="bg-white p-5 sm:p-6 rounded-lg shadow-sm border border-slate-100 hover:shadow-md transition-all duration-300 relative overflow-hidden group">
    <div className="flex items-center">
      <div className="p-3 sm:p-4 rounded-lg bg-blue-50 text-blue-500 mr-4 sm:mr-5 group-hover:bg-blue-100 transition-colors duration-300">
        {icon}
      </div>
      <div>
        <p className="text-xs sm:text-sm text-slate-500 mb-1">{title}</p>
        <p className="text-2xl sm:text-3xl font-semibold text-slate-800">{value}</p>
        {subtitle && <p className="text-xs text-slate-500 mt-1">{subtitle}</p>}
      </div>
    </div>
  </div>
);

export default function OverviewStats() {
  const [stats, setStats] = useState<AnalyticsOverview | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchStats = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`${window.location.origin}/api/admin/analytics/overview`);

      if (!response.ok) {
        throw new Error('Failed to fetch analytics overview');
      }

      const data = await response.json();
      setStats(data);
      setLastUpdated(new Date());
    } catch (err) {
      console.error('Error fetching analytics overview:', err);
      setError('Failed to load analytics overview');
      toast.error('Failed to load analytics overview');
    } finally {
      setIsLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchStats();
  }, []);

  // Handle refresh
  const handleRefresh = () => {
    fetchStats();
  };

  // Render loading state
  if (isLoading && !stats) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 animate-pulse">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white p-6 rounded-lg shadow-sm border border-slate-100 h-32">
            <div className="flex items-center">
              <div className="w-12 h-12 rounded-lg bg-slate-200 mr-4"></div>
              <div className="space-y-2">
                <div className="h-4 w-20 bg-slate-200 rounded"></div>
                <div className="h-8 w-16 bg-slate-200 rounded"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  // Render error state
  if (error && !stats) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6 text-center">
        <p className="text-red-500 mb-4">{error}</p>
        <button
          onClick={handleRefresh}
          className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold text-slate-800">Overview</h2>
        <div className="flex items-center space-x-2">
          {stats && (
            <div className="flex items-center">
              <span
                className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full mr-2 ${
                  stats.isRealData
                    ? 'bg-green-100 text-green-800'
                    : 'bg-amber-100 text-amber-800'
                }`}
              >
                {stats.isRealData ? 'Real Data' : 'Mock Data'}
              </span>
            </div>
          )}
          {lastUpdated && (
            <span className="text-xs text-slate-500">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </span>
          )}
          <button
            onClick={handleRefresh}
            disabled={isLoading}
            className="p-2 rounded-lg text-slate-500 hover:text-slate-700 hover:bg-slate-100 transition-colors"
            aria-label="Refresh statistics"
          >
            <ArrowPathIcon className={`h-5 w-5 ${isLoading ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      {stats && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
          <StatCard
            title="Page Views"
            value={stats.pageViews.toLocaleString()}
            icon={<ChartBarIcon className="h-5 w-5 sm:h-6 sm:w-6" />}
          />
          <StatCard
            title="Unique Visitors"
            value={stats.uniqueVisitors.toLocaleString()}
            icon={<UsersIcon className="h-5 w-5 sm:h-6 sm:w-6" />}
          />
          <StatCard
            title="Bounce Rate"
            value={`${stats.bounceRate}%`}
            icon={<ArrowTrendingUpIcon className="h-5 w-5 sm:h-6 sm:w-6" />}
          />
          <StatCard
            title="Avg. Session Duration"
            value={stats.avgSessionDuration}
            icon={<ClockIcon className="h-5 w-5 sm:h-6 sm:w-6" />}
          />
        </div>
      )}
    </div>
  );
}
