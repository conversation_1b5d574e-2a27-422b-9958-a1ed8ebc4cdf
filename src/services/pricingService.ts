import prisma from '@/lib/prisma';

export interface Pricing {
  id: string;
  service: string;
  price: number;
  description?: string;
  features?: string[];
  icon?: string;
  popular?: boolean;
  imageUrl?: string;
  imageUrl2?: string;
  imageUrl3?: string;
  category?: string;
  createdAt: string;
  updatedAt: string;
}

// Get all pricing items
export async function getAllPricing(): Promise<Pricing[]> {
  try {
    console.log('Service: Fetching all pricing items from database');
    const pricingItems = await prisma.pricing.findMany({
      orderBy: {
        service: 'asc'
      }
    });

    console.log(`Service: Found ${pricingItems.length} pricing items in database`);

    if (pricingItems.length > 0) {
      console.log('Service: Sample pricing item from DB:', JSON.stringify(pricingItems[0]));
    }

    const formattedItems = pricingItems.map(formatPricing);

    if (formattedItems.length > 0) {
      console.log('Service: Sample formatted pricing item:', JSON.stringify(formattedItems[0]));
    }

    return formattedItems;
  } catch (error) {
    console.error('Service: Error getting all pricing items:', error);
    throw new Error('Failed to fetch pricing items from database');
  }
}

// Get a pricing item by ID
export async function getPricingById(id: string): Promise<Pricing | null> {
  try {
    const pricingId = parseInt(id);
    const pricing = await prisma.pricing.findUnique({
      where: {
        id: pricingId
      }
    });

    if (!pricing) {
      return null;
    }

    return formatPricing(pricing);
  } catch (error) {
    console.error(`Error getting pricing by ID ${id}:`, error);
    throw error;
  }
}

// Create a new pricing item
export async function createPricing(data: Omit<Pricing, 'id' | 'createdAt' | 'updatedAt'>): Promise<Pricing> {
  try {
    // Create pricing data with all fields
    const newPricing = await prisma.pricing.create({
      data: {
        service: data.service,
        price: data.price,
        description: data.description || '',
        features: data.features || [],
        icon: data.icon,
        popular: data.popular || false,
        imageUrl: data.imageUrl,
        imageUrl2: data.imageUrl2,
        imageUrl3: data.imageUrl3,
        category: data.category || 'Other'
      }
    });

    // Return the formatted pricing item
    return formatPricing(newPricing);
  } catch (error) {
    console.error('Error creating pricing item:', error);
    throw error;
  }
}

// Update a pricing item
export async function updatePricing(id: string, data: Partial<Pricing>): Promise<Pricing | null> {
  try {
    const pricingId = parseInt(id);

    // Check if the pricing item exists
    const existingPricing = await prisma.pricing.findUnique({
      where: {
        id: pricingId
      }
    });

    if (!existingPricing) {
      return null;
    }

    // Create an update data object with only the fields that are provided
    const updateData: any = {};
    if (data.service !== undefined) updateData.service = data.service;
    if (data.price !== undefined) updateData.price = data.price;
    if (data.description !== undefined) updateData.description = data.description;
    if (data.features !== undefined) updateData.features = data.features;
    if (data.icon !== undefined) updateData.icon = data.icon;
    if (data.popular !== undefined) updateData.popular = data.popular;
    if (data.imageUrl !== undefined) updateData.imageUrl = data.imageUrl;
    if (data.imageUrl2 !== undefined) updateData.imageUrl2 = data.imageUrl2;
    if (data.imageUrl3 !== undefined) updateData.imageUrl3 = data.imageUrl3;
    if (data.category !== undefined) updateData.category = data.category;

    const updatedPricing = await prisma.pricing.update({
      where: {
        id: pricingId
      },
      data: updateData
    });

    // Return the formatted pricing item
    return formatPricing(updatedPricing);
  } catch (error) {
    console.error(`Error updating pricing ${id}:`, error);
    throw error;
  }
}

// Delete a pricing item
export async function deletePricing(id: string): Promise<boolean> {
  try {
    const pricingId = parseInt(id);

    await prisma.pricing.delete({
      where: {
        id: pricingId
      }
    });

    return true;
  } catch (error) {
    console.error(`Error deleting pricing ${id}:`, error);
    return false;
  }
}

// Bulk delete pricing items
export async function bulkDeletePricing(ids: string[]): Promise<{ success: boolean, count: number }> {
  try {
    // Convert string IDs to integers
    const pricingIds = ids.map(id => parseInt(id));

    // Delete all pricing items with the given IDs
    const result = await prisma.pricing.deleteMany({
      where: {
        id: {
          in: pricingIds
        }
      }
    });

    return {
      success: true,
      count: result.count
    };
  } catch (error) {
    console.error(`Error bulk deleting pricing items:`, error);
    return {
      success: false,
      count: 0
    };
  }
}

// Helper function to format pricing data from the database
function formatPricing(pricing: any): Pricing {
  // The database might not have the new fields yet, so we'll handle that case
  return {
    id: pricing.id.toString(),
    service: pricing.service,
    price: pricing.price,
    description: pricing.description || '',
    // These fields might not exist in the database yet
    features: pricing.features || [],
    icon: pricing.icon || undefined,
    popular: pricing.popular || false,
    imageUrl: pricing.imageUrl || undefined,
    imageUrl2: pricing.imageUrl2 || undefined,
    imageUrl3: pricing.imageUrl3 || undefined,
    category: pricing.category || 'Other',
    createdAt: pricing.createdAt.toISOString(),
    updatedAt: pricing.updatedAt.toISOString()
  };
}