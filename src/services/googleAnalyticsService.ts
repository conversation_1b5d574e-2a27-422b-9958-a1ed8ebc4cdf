/**
 * Google Analytics API Service
 *
 * This service provides functions to interact with the Google Analytics API
 * and retrieve analytics data for the website.
 */

import { ErrorTracker } from './errorTracking';
import { BetaAnalyticsDataClient } from '@google-analytics/data';

// Define types for analytics data
export interface AnalyticsOverview {
  pageViews: number;
  uniqueVisitors: number;
  bounceRate: number;
  avgSessionDuration: string;
  sessions: number;
  isRealData: boolean;
}

export interface TrafficSource {
  source: string;
  sessions: number;
  percentage: number;
  isRealData: boolean;
}

export interface PageViewData {
  date: string;
  views: number;
}

export interface TopPage {
  path: string;
  pageViews: number;
  uniquePageViews: number;
  avgTimeOnPage: string;
  bounceRate: number;
  isRealData: boolean;
}

export interface DeviceData {
  device: string;
  sessions: number;
  percentage: number;
  isRealData: boolean;
}

export interface CountryData {
  country: string;
  sessions: number;
  percentage: number;
  isRealData: boolean;
}

// Initialize the Google Analytics Data API client
const analyticsDataClient = new BetaAnalyticsDataClient();
const propertyId = process.env.GOOGLE_ANALYTICS_PROPERTY_ID || 'properties/11238428380';

// Helper function to format duration in seconds to a readable format
function formatDuration(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}m ${remainingSeconds}s`;
}

/**
 * Get analytics overview data from Google Analytics
 */
export async function getAnalyticsOverview(): Promise<AnalyticsOverview> {
  try {
    // Check if we have a valid property ID
    if (!propertyId || propertyId === 'properties/123456789') {
      console.warn('No valid Google Analytics property ID found. Using mock data.');
      return getMockAnalyticsOverview();
    }

    // Make the API request to Google Analytics
    const [response] = await analyticsDataClient.runReport({
      property: propertyId,
      dateRanges: [
        {
          startDate: '30daysAgo',
          endDate: 'today',
        },
      ],
      metrics: [
        { name: 'screenPageViews' },
        { name: 'totalUsers' },
        { name: 'bounceRate' },
        { name: 'averageSessionDuration' },
        { name: 'sessions' },
      ],
    });

    // If we don't have any rows, return mock data
    if (!response.rows || response.rows.length === 0) {
      console.warn('No data returned from Google Analytics. Using mock data.');
      return getMockAnalyticsOverview();
    }

    // Extract the metrics from the response
    const metrics = response.rows[0].metricValues || [];

    return {
      pageViews: parseInt(metrics[0]?.value || '0', 10),
      uniqueVisitors: parseInt(metrics[1]?.value || '0', 10),
      bounceRate: parseFloat(metrics[2]?.value || '0'),
      avgSessionDuration: formatDuration(parseFloat(metrics[3]?.value || '0')),
      sessions: parseInt(metrics[4]?.value || '0', 10),
      isRealData: true
    };
  } catch (error) {
    console.error('Error fetching analytics overview from Google Analytics:', error);
    ErrorTracker.trackError(error as Error, 'getAnalyticsOverview');

    // Return mock data as fallback
    return getMockAnalyticsOverview();
  }
}

/**
 * Mock function to get analytics overview data as fallback
 */
function getMockAnalyticsOverview(): Promise<AnalyticsOverview> {
  return Promise.resolve({
    pageViews: 12458,
    uniqueVisitors: 5842,
    bounceRate: 42.3,
    avgSessionDuration: '2m 15s',
    sessions: 7891,
    isRealData: false
  });
}

/**
 * Get traffic sources data from Google Analytics
 */
export async function getTrafficSources(): Promise<TrafficSource[]> {
  try {
    // Check if we have a valid property ID
    if (!propertyId || propertyId === 'properties/123456789') {
      console.warn('No valid Google Analytics property ID found. Using mock data.');
      return getMockTrafficSources();
    }

    // Make the API request to Google Analytics
    const [response] = await analyticsDataClient.runReport({
      property: propertyId,
      dateRanges: [
        {
          startDate: '30daysAgo',
          endDate: 'today',
        },
      ],
      dimensions: [
        { name: 'sessionSource' },
      ],
      metrics: [
        { name: 'sessions' },
      ],
      orderBys: [
        {
          metric: { metricName: 'sessions' },
          desc: true,
        },
      ],
      limit: 5,
    });

    // If we don't have any rows, return mock data
    if (!response.rows || response.rows.length === 0) {
      console.warn('No traffic source data returned from Google Analytics. Using mock data.');
      return getMockTrafficSources();
    }

    // Calculate total sessions for percentage calculation
    const totalSessions = response.rows.reduce(
      (sum, row) => sum + parseInt(row.metricValues?.[0]?.value || '0', 10),
      0
    );

    // Map the response to our TrafficSource interface
    const trafficSources: TrafficSource[] = response.rows.map((row) => {
      const sessions = parseInt(row.metricValues?.[0]?.value || '0', 10);
      const percentage = totalSessions > 0 ? (sessions / totalSessions) * 100 : 0;

      return {
        source: row.dimensionValues?.[0]?.value || '(not set)',
        sessions,
        percentage: parseFloat(percentage.toFixed(1)),
        isRealData: true
      };
    });

    return trafficSources;
  } catch (error) {
    console.error('Error fetching traffic sources from Google Analytics:', error);
    ErrorTracker.trackError(error as Error, 'getTrafficSources');

    // Return mock data as fallback
    return getMockTrafficSources();
  }
}

/**
 * Mock function to get traffic sources data as fallback
 */
function getMockTrafficSources(): Promise<TrafficSource[]> {
  return Promise.resolve([
    { source: 'Organic Search', sessions: 3245, percentage: 41.1, isRealData: false },
    { source: 'Direct', sessions: 2156, percentage: 27.3, isRealData: false },
    { source: 'Social', sessions: 1245, percentage: 15.8, isRealData: false },
    { source: 'Referral', sessions: 876, percentage: 11.1, isRealData: false },
    { source: 'Email', sessions: 369, percentage: 4.7, isRealData: false }
  ]);
}

/**
 * Get page view data over time from Google Analytics
 */
export async function getPageViewsOverTime(days: number = 30): Promise<PageViewData[]> {
  try {
    // Check if we have a valid property ID
    if (!propertyId || propertyId === 'properties/123456789') {
      console.warn('No valid Google Analytics property ID found. Using mock data.');
      return getMockPageViewsOverTime(days);
    }

    // Make the API request to Google Analytics
    const [response] = await analyticsDataClient.runReport({
      property: propertyId,
      dateRanges: [
        {
          startDate: `${days}daysAgo`,
          endDate: 'today',
        },
      ],
      dimensions: [
        { name: 'date' },
      ],
      metrics: [
        { name: 'screenPageViews' },
      ],
      orderBys: [
        {
          dimension: { dimensionName: 'date' },
          desc: false,
        },
      ],
    });

    // If we don't have any rows, return mock data
    if (!response.rows || response.rows.length === 0) {
      console.warn('No page view data returned from Google Analytics. Using mock data.');
      return getMockPageViewsOverTime(days);
    }

    // Map the response to our PageViewData interface
    const pageViewData: PageViewData[] = response.rows.map((row) => {
      return {
        date: formatGADate(row.dimensionValues?.[0]?.value || ''),
        views: parseInt(row.metricValues?.[0]?.value || '0', 10),
      };
    });

    return pageViewData;
  } catch (error) {
    console.error('Error fetching page views over time from Google Analytics:', error);
    ErrorTracker.trackError(error as Error, 'getPageViewsOverTime');

    // Return mock data as fallback
    return getMockPageViewsOverTime(days);
  }
}

/**
 * Format Google Analytics date (YYYYMMDD) to YYYY-MM-DD
 */
function formatGADate(gaDate: string): string {
  if (gaDate.length !== 8) return gaDate;

  const year = gaDate.substring(0, 4);
  const month = gaDate.substring(4, 6);
  const day = gaDate.substring(6, 8);

  return `${year}-${month}-${day}`;
}

/**
 * Mock function to get page view data over time as fallback
 */
function getMockPageViewsOverTime(days: number = 30): Promise<PageViewData[]> {
  // Generate mock data for the specified number of days
  const data: PageViewData[] = [];
  const today = new Date();

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);

    // Generate a somewhat realistic pattern with weekends having less traffic
    const dayOfWeek = date.getDay(); // 0 = Sunday, 6 = Saturday
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;

    // Base views with some randomness
    const baseViews = isWeekend ? 300 : 500;
    const randomFactor = Math.random() * 0.4 + 0.8; // 0.8 to 1.2
    const views = Math.floor(baseViews * randomFactor);

    data.push({
      date: date.toISOString().split('T')[0], // YYYY-MM-DD format
      views
    });
  }

  return Promise.resolve(data);
}

/**
 * Get top performing pages from Google Analytics
 */
export async function getTopPages(limit: number = 10): Promise<TopPage[]> {
  try {
    // Check if we have a valid property ID
    if (!propertyId || propertyId === 'properties/123456789') {
      console.warn('No valid Google Analytics property ID found. Using mock data.');
      return getMockTopPages(limit);
    }

    // Make the API request to Google Analytics
    const [response] = await analyticsDataClient.runReport({
      property: propertyId,
      dateRanges: [
        {
          startDate: '30daysAgo',
          endDate: 'today',
        },
      ],
      dimensions: [
        { name: 'pagePath' },
      ],
      metrics: [
        { name: 'screenPageViews' },
        { name: 'screenPageViewsPerSession' },
        { name: 'averageSessionDuration' },
        { name: 'bounceRate' },
      ],
      orderBys: [
        {
          metric: { metricName: 'screenPageViews' },
          desc: true,
        },
      ],
      limit: limit,
    });

    // If we don't have any rows, return mock data
    if (!response.rows || response.rows.length === 0) {
      console.warn('No top pages data returned from Google Analytics. Using mock data.');
      return getMockTopPages(limit);
    }

    // Map the response to our TopPage interface
    const topPages: TopPage[] = response.rows.map((row) => {
      const pageViews = parseInt(row.metricValues?.[0]?.value || '0', 10);
      const viewsPerSession = parseFloat(row.metricValues?.[1]?.value || '0');
      const uniquePageViews = Math.round(pageViews / (viewsPerSession > 0 ? viewsPerSession : 1));

      return {
        path: row.dimensionValues?.[0]?.value || '/',
        pageViews,
        uniquePageViews,
        avgTimeOnPage: formatDuration(parseFloat(row.metricValues?.[2]?.value || '0')),
        bounceRate: parseFloat(parseFloat(row.metricValues?.[3]?.value || '0').toFixed(1)),
        isRealData: true
      };
    });

    return topPages;
  } catch (error) {
    console.error('Error fetching top pages from Google Analytics:', error);
    ErrorTracker.trackError(error as Error, 'getTopPages');

    // Return mock data as fallback
    return getMockTopPages(limit);
  }
}

/**
 * Mock function to get top performing pages as fallback
 */
function getMockTopPages(limit: number = 10): Promise<TopPage[]> {
  return Promise.resolve([
    { path: '/', pageViews: 3245, uniquePageViews: 2567, avgTimeOnPage: '1m 45s', bounceRate: 35.2, isRealData: false },
    { path: '/web-development', pageViews: 1876, uniquePageViews: 1543, avgTimeOnPage: '2m 12s', bounceRate: 40.1, isRealData: false },
    { path: '/logo-design', pageViews: 1654, uniquePageViews: 1321, avgTimeOnPage: '1m 58s', bounceRate: 38.7, isRealData: false },
    { path: '/digital-marketing', pageViews: 1432, uniquePageViews: 1198, avgTimeOnPage: '1m 36s', bounceRate: 42.3, isRealData: false },
    { path: '/blog', pageViews: 1245, uniquePageViews: 987, avgTimeOnPage: '2m 24s', bounceRate: 30.5, isRealData: false },
    { path: '/contact', pageViews: 876, uniquePageViews: 765, avgTimeOnPage: '1m 12s', bounceRate: 45.8, isRealData: false },
    { path: '/about', pageViews: 765, uniquePageViews: 654, avgTimeOnPage: '1m 32s', bounceRate: 41.2, isRealData: false },
    { path: '/portfolio', pageViews: 654, uniquePageViews: 543, avgTimeOnPage: '2m 05s', bounceRate: 36.9, isRealData: false },
    { path: '/pricing', pageViews: 543, uniquePageViews: 432, avgTimeOnPage: '1m 48s', bounceRate: 39.4, isRealData: false },
    { path: '/blog/web-design-trends', pageViews: 432, uniquePageViews: 345, avgTimeOnPage: '3m 15s', bounceRate: 25.7, isRealData: false }
  ].slice(0, limit));
}

/**
 * Get device usage data from Google Analytics
 */
export async function getDeviceData(): Promise<DeviceData[]> {
  try {
    // Check if we have a valid property ID
    if (!propertyId || propertyId === 'properties/123456789') {
      console.warn('No valid Google Analytics property ID found. Using mock data.');
      return getMockDeviceData();
    }

    // Make the API request to Google Analytics
    const [response] = await analyticsDataClient.runReport({
      property: propertyId,
      dateRanges: [
        {
          startDate: '30daysAgo',
          endDate: 'today',
        },
      ],
      dimensions: [
        { name: 'deviceCategory' },
      ],
      metrics: [
        { name: 'sessions' },
      ],
      orderBys: [
        {
          metric: { metricName: 'sessions' },
          desc: true,
        },
      ],
    });

    // If we don't have any rows, return mock data
    if (!response.rows || response.rows.length === 0) {
      console.warn('No device data returned from Google Analytics. Using mock data.');
      return getMockDeviceData();
    }

    // Calculate total sessions for percentage calculation
    const totalSessions = response.rows.reduce(
      (sum, row) => sum + parseInt(row.metricValues?.[0]?.value || '0', 10),
      0
    );

    // Map the response to our DeviceData interface
    const deviceData: DeviceData[] = response.rows.map((row) => {
      const sessions = parseInt(row.metricValues?.[0]?.value || '0', 10);
      const percentage = totalSessions > 0 ? (sessions / totalSessions) * 100 : 0;

      return {
        device: row.dimensionValues?.[0]?.value || 'Unknown',
        sessions,
        percentage: parseFloat(percentage.toFixed(1)),
        isRealData: true
      };
    });

    return deviceData;
  } catch (error) {
    console.error('Error fetching device data from Google Analytics:', error);
    ErrorTracker.trackError(error as Error, 'getDeviceData');

    // Return mock data as fallback
    return getMockDeviceData();
  }
}

/**
 * Mock function to get device usage data as fallback
 */
function getMockDeviceData(): Promise<DeviceData[]> {
  return Promise.resolve([
    { device: 'Mobile', sessions: 4256, percentage: 53.9, isRealData: false },
    { device: 'Desktop', sessions: 2987, percentage: 37.9, isRealData: false },
    { device: 'Tablet', sessions: 648, percentage: 8.2, isRealData: false }
  ]);
}

/**
 * Get country data from Google Analytics
 */
export async function getCountryData(limit: number = 5): Promise<CountryData[]> {
  try {
    // Check if we have a valid property ID
    if (!propertyId || propertyId === 'properties/123456789') {
      console.warn('No valid Google Analytics property ID found. Using mock data.');
      return getMockCountryData(limit);
    }

    // Make the API request to Google Analytics
    const [response] = await analyticsDataClient.runReport({
      property: propertyId,
      dateRanges: [
        {
          startDate: '30daysAgo',
          endDate: 'today',
        },
      ],
      dimensions: [
        { name: 'country' },
      ],
      metrics: [
        { name: 'sessions' },
      ],
      orderBys: [
        {
          metric: { metricName: 'sessions' },
          desc: true,
        },
      ],
      limit: limit,
    });

    // If we don't have any rows, return mock data
    if (!response.rows || response.rows.length === 0) {
      console.warn('No country data returned from Google Analytics. Using mock data.');
      return getMockCountryData(limit);
    }

    // Calculate total sessions for percentage calculation
    const totalSessions = response.rows.reduce(
      (sum, row) => sum + parseInt(row.metricValues?.[0]?.value || '0', 10),
      0
    );

    // Map the response to our CountryData interface
    const countryData: CountryData[] = response.rows.map((row) => {
      const sessions = parseInt(row.metricValues?.[0]?.value || '0', 10);
      const percentage = totalSessions > 0 ? (sessions / totalSessions) * 100 : 0;

      return {
        country: row.dimensionValues?.[0]?.value || 'Unknown',
        sessions,
        percentage: parseFloat(percentage.toFixed(1)),
        isRealData: true
      };
    });

    return countryData;
  } catch (error) {
    console.error('Error fetching country data from Google Analytics:', error);
    ErrorTracker.trackError(error as Error, 'getCountryData');

    // Return mock data as fallback
    return getMockCountryData(limit);
  }
}

/**
 * Mock function to get country data as fallback
 */
function getMockCountryData(limit: number = 5): Promise<CountryData[]> {
  return Promise.resolve([
    { country: 'Kenya', sessions: 5432, percentage: 68.8, isRealData: false },
    { country: 'United States', sessions: 876, percentage: 11.1, isRealData: false },
    { country: 'United Kingdom', sessions: 543, percentage: 6.9, isRealData: false },
    { country: 'South Africa', sessions: 321, percentage: 4.1, isRealData: false },
    { country: 'Nigeria', sessions: 287, percentage: 3.6, isRealData: false },
    { country: 'Uganda', sessions: 198, percentage: 2.5, isRealData: false },
    { country: 'Tanzania', sessions: 145, percentage: 1.8, isRealData: false },
    { country: 'Canada', sessions: 89, percentage: 1.1, isRealData: false }
  ].slice(0, limit));
}
