import prisma from '@/lib/prisma';

export interface SimplePricing {
  id: string;
  service: string;
  price: number;
  description?: string;
  features?: string[];
  icon?: string;
  popular?: boolean;
  imageUrl?: string;
  imageUrl2?: string;
  imageUrl3?: string;
  category?: string;
  createdAt: string;
  updatedAt: string;
}

// Get all pricing items
export async function getAllPricing(): Promise<SimplePricing[]> {
  try {
    console.log('Service: Fetching all pricing items from database');
    const pricingItems = await prisma.pricing.findMany({
      orderBy: {
        service: 'asc'
      }
    });

    console.log(`Service: Found ${pricingItems.length} pricing items in database`);

    if (pricingItems.length > 0) {
      console.log('Service: Sample pricing item from DB:', JSON.stringify(pricingItems[0]));
    }

    const formattedItems = pricingItems.map(formatPricing);

    if (formattedItems.length > 0) {
      console.log('Service: Sample formatted pricing item:', JSON.stringify(formattedItems[0]));
    }

    return formattedItems;
  } catch (error) {
    console.error('Service: Error getting all pricing items:', error);
    throw new Error('Failed to fetch pricing items from database');
  }
}

// Get a pricing item by ID
export async function getPricingById(id: string): Promise<SimplePricing | null> {
  try {
    const pricingId = parseInt(id);
    const pricing = await prisma.pricing.findUnique({
      where: {
        id: pricingId
      }
    });

    if (!pricing) {
      return null;
    }

    return formatPricing(pricing);
  } catch (error) {
    console.error(`Error getting pricing by ID ${id}:`, error);
    throw error;
  }
}

// Helper function to format pricing data from the database
function formatPricing(pricing: any): SimplePricing {
  return {
    id: pricing.id.toString(),
    service: pricing.service,
    price: pricing.price,
    description: pricing.description || '',
    features: pricing.features || [],
    icon: pricing.icon || undefined,
    popular: pricing.popular || false,
    imageUrl: pricing.imageUrl || undefined,
    imageUrl2: pricing.imageUrl2 || undefined,
    imageUrl3: pricing.imageUrl3 || undefined,
    category: pricing.category || 'Other',
    createdAt: pricing.createdAt.toISOString(),
    updatedAt: pricing.updatedAt.toISOString()
  };
}
