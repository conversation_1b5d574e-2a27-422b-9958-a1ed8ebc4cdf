import { NextRequest, NextResponse } from 'next/server';
import * as pricingService from '@/services/simplePricingService';

export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');

    // Get the pricing items from the database
    console.log('API: Fetching pricing items from database...');
    const pricingItems = await pricingService.getAllPricing();

    // Check if we have any pricing items
    if (!pricingItems || pricingItems.length === 0) {
      console.log('API: No pricing items found in database');
      return NextResponse.json([], {
        headers: {
          'Cache-Control': 'public, max-age=60, s-maxage=120', // Cache for 1 minute
          'Expires': new Date(Date.now() + 60000).toUTCString() // 1 minute
        }
      });
    }

    console.log(`API: Retrieved ${pricingItems.length} pricing items from database`);

    // Filter by category if provided
    let filteredItems = pricingItems;
    if (category) {
      console.log(`API: Filtering by category: ${category}`);
      filteredItems = pricingItems.filter(item =>
        (item.category || 'Other') === category
      );
      console.log(`API: Found ${filteredItems.length} items in category "${category}"`);
    }

    // Add cache control headers - shorter cache time to ensure fresh data
    return NextResponse.json(filteredItems, {
      headers: {
        'Cache-Control': 'public, max-age=60, s-maxage=120', // Cache for 1 minute
        'Expires': new Date(Date.now() + 60000).toUTCString() // 1 minute
      }
    });
  } catch (error) {
    console.error('Error fetching pricing items:', error);
    return new NextResponse(JSON.stringify({
      error: 'Internal Server Error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
}