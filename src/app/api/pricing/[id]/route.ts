import { NextRequest, NextResponse } from 'next/server';
import * as pricingService from '@/services/pricingService';

// GET handler - Get a specific pricing item by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    console.log(`API route: Fetching pricing item with ID: ${id}`);

    // Get the pricing item from database
    const pricingItem = await pricingService.getPricingById(id);

    if (!pricingItem) {
      console.log(`Pricing item not found: ${id}`);
      return NextResponse.json(
        { error: 'Pricing item not found' },
        { status: 404 }
      );
    }

    console.log(`Successfully fetched pricing item: ${pricingItem.service}`);

    return NextResponse.json(pricingItem, {
      headers: {
        'Cache-Control': 'no-store',
      },
    });
  } catch (error) {
    console.error(`Error in GET pricing item:`, error);
    return NextResponse.json(
      { error: 'Failed to fetch pricing item' },
      { status: 500 }
    );
  }
}
