import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import * as pricingService from '@/services/pricingService';

interface RouteParams {
  params: {
    id: string;
  };
}

export async function GET(request: Request, { params }: RouteParams) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
      });
    }

    const { id } = params;
    const pricing = await pricingService.getPricingById(id);

    if (!pricing) {
      return new NextResponse(JSON.stringify({ error: 'Pricing item not found' }), {
        status: 404,
      });
    }

    return NextResponse.json(pricing);
  } catch (error) {
    console.error(`Error fetching pricing item ${params.id}:`, error);
    return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
    });
  }
}

export async function PUT(request: Request, { params }: RouteParams) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
      });
    }

    const { id } = params;
    const body = await request.json();
    const {
      service,
      price,
      description,
      icon,
      popular,
      features,
      imageUrl,
      imageUrl2,
      imageUrl3,
      category
    } = body;

    // Validate required fields
    if (!service) {
      return new NextResponse(JSON.stringify({ error: 'Service name is required' }), {
        status: 400,
      });
    }

    if (price === undefined || price === null) {
      return new NextResponse(JSON.stringify({ error: 'Price is required' }), {
        status: 400,
      });
    }

    const updatedPricing = await pricingService.updatePricing(id, {
      service,
      price: Number(price),
      description,
      icon,
      popular,
      features: Array.isArray(features) ? features : undefined,
      imageUrl,
      imageUrl2,
      imageUrl3,
      category
    });

    if (!updatedPricing) {
      return new NextResponse(JSON.stringify({ error: 'Pricing item not found' }), {
        status: 404,
      });
    }

    return NextResponse.json(updatedPricing);
  } catch (error) {
    console.error(`Error updating pricing item ${params.id}:`, error);
    return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
    });
  }
}

export async function DELETE(request: Request, { params }: RouteParams) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
      });
    }

    const { id } = params;
    const success = await pricingService.deletePricing(id);

    if (!success) {
      return new NextResponse(JSON.stringify({ error: 'Failed to delete pricing item' }), {
        status: 500,
      });
    }

    return new NextResponse(JSON.stringify({ success: true }), {
      status: 200,
    });
  } catch (error) {
    console.error(`Error deleting pricing item ${params.id}:`, error);
    return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
    });
  }
}