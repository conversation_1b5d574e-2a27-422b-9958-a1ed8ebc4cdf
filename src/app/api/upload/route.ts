import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import crypto from 'crypto';
import { uploadImageToS3 } from '@/utils/s3Utils';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

// Maximum file size (5MB)
const MAX_FILE_SIZE = 5 * 1024 * 1024;

// Valid file types
const VALID_FILE_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];

export async function POST(request: Request) {
  try {
    // Check authentication for admin routes
    const session = await getServerSession(authOptions);
    const isAdminRoute = request.url.includes('/api/admin/');

    if (isAdminRoute && !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse the multipart form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const category = formData.get('category') as string || 'pricing';

    // Log received data
    console.log('Received upload request:', {
      filename: file?.name,
      category,
      fileType: file?.type,
      fileSize: file?.size
    });

    // Validate inputs
    if (!file) {
      console.error('Upload error: No file provided');
      return NextResponse.json(
        { error: 'File is required' },
        { status: 400 }
      );
    }

    // Validate file type
    if (!VALID_FILE_TYPES.includes(file.type)) {
      console.error(`Upload error: Invalid file type ${file.type}`);
      return NextResponse.json(
        { error: `Invalid file type. Allowed types: ${VALID_FILE_TYPES.join(', ')}` },
        { status: 400 }
      );
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      console.error(`Upload error: File size ${file.size} exceeds limit ${MAX_FILE_SIZE}`);
      return NextResponse.json(
        { error: `File size exceeds the maximum allowed size of ${MAX_FILE_SIZE / (1024 * 1024)}MB` },
        { status: 400 }
      );
    }

    // Check if S3 environment variables are set
    if (!process.env.NEXT_PUBLIC_S3_BUCKET ||
        !process.env.NEXT_PUBLIC_S3_ACCESS_KEY ||
        !process.env.NEXT_PUBLIC_S3_SECRET_KEY ||
        !process.env.NEXT_PUBLIC_S3_ENDPOINT) {
      console.error('Upload error: Missing S3 environment variables');
      return NextResponse.json(
        { error: 'Server storage configuration is incomplete. Please contact the administrator.' },
        { status: 500 }
      );
    }

    // Generate unique filename
    const buffer = await file.arrayBuffer();
    const hash = crypto.createHash('sha256')
      .update(Buffer.from(buffer))
      .digest('hex')
      .slice(0, 8);

    const ext = file.name.split('.').pop();
    const filename = `${hash}-${Date.now()}.${ext}`;

    // Determine the S3 key based on category
    // Make sure the category is valid and create the appropriate path
    const validCategory = category && typeof category === 'string'
      ? category.toLowerCase().trim()
      : 'pricing';

    // Ensure the directory structure exists for pricing products
    const s3Key = `images/${validCategory}/${filename}`;

    console.log(`Attempting to upload to S3 with key: ${s3Key} for category: ${validCategory}`);

    // Upload to S3
    const result = await uploadImageToS3(file, s3Key, {
      logPrefix: '[PricingUpload]',
      maxRetries: 3,
      timeoutMs: 60000 // 60 seconds
    });

    if (!result.success) {
      console.error('S3 upload error:', result.error);

      // Check if we have a fallback URL (for example, a placeholder image)
      const fallbackUrl = '/images/placeholder.jpg';

      // If this is a non-critical upload (like an optional image), we can return a warning instead of an error
      const isOptionalImage = key.includes('imageUrl2') || key.includes('imageUrl3');

      if (isOptionalImage) {
        console.warn('Optional image upload failed, continuing with fallback');
        return NextResponse.json({
          url: fallbackUrl,
          key: 'fallback',
          filename: 'placeholder.jpg',
          warning: 'Image upload failed, using placeholder image instead. ' + (result.error || 'Unknown error')
        });
      }

      // For critical uploads, return an error
      return NextResponse.json(
        {
          error: result.error || 'Failed to upload file to storage',
          errorCode: 'S3_UPLOAD_FAILED'
        },
        { status: 500 }
      );
    }

    console.log('Upload successful:', {
      url: result.url,
      key: s3Key,
      filename
    });

    // Return the URL with success message
    return NextResponse.json({
      url: result.url,
      key: s3Key,
      filename,
      success: true,
      message: 'Image uploaded successfully'
    });
  } catch (error) {
    console.error('Upload error:', error);

    // Provide a more detailed error message
    let errorMessage = 'Failed to upload file';
    if (error instanceof Error) {
      // Clean up any confusing error messages
      errorMessage = error.message
        .replace('AI image uploads failed', 'Image upload failed')
        .replace('Please check your commission privacy again', 'Please try again');
    }

    return NextResponse.json(
      {
        error: errorMessage,
        errorCode: 'UPLOAD_EXCEPTION',
        // Include a fallback URL for non-critical uploads
        fallbackUrl: '/images/placeholder.jpg'
      },
      { status: 500 }
    );
  }
}