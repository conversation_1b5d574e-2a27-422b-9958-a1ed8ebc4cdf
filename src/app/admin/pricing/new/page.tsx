'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';

export default function NewPricingPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    service: '',
    price: '',
    description: '',
    icon: '',
    popular: false,
    features: ['', '', ''], // Start with 3 empty feature fields
    imageUrl: '',
    imageUrl2: '',
    imageUrl3: '',
    category: 'Other'
  });

  // For image upload
  const [imageFiles, setImageFiles] = useState<(File | null)[]>([null, null, null]);
  const [imagePreviews, setImagePreviews] = useState<string[]>(['', '', '']);
  const [uploadingImage, setUploadingImage] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement; // Cast to access 'type' and 'checked'

    if (type === 'checkbox') {
      setFormData((prev) => ({ ...prev, [name]: (e.target as HTMLInputElement).checked }));
    } else if (name.startsWith('feature-')) {
      // Handle feature updates
      const index = parseInt(name.split('-')[1]);
      const newFeatures = [...formData.features];
      newFeatures[index] = value;
      setFormData((prev) => ({ ...prev, features: newFeatures }));
    } else if (type === 'file') {
      // Handle image file upload
      const files = (e.target as HTMLInputElement).files;
      if (files && files.length > 0) {
        const file = files[0];

        // Extract the image index from the input name or id
        // The format is "image-X" where X is the index (0, 1, or 2)
        let imageIndex = 0;
        const idMatch = (e.target as HTMLInputElement).id.match(/image-(\d+)/);
        if (idMatch && idMatch[1]) {
          imageIndex = parseInt(idMatch[1]);
        }

        console.log(`Uploading image at index ${imageIndex} from input ${(e.target as HTMLInputElement).id}`);

        // Update the specific image file in the array
        const newImageFiles = [...imageFiles];
        newImageFiles[imageIndex] = file;
        setImageFiles(newImageFiles);

        // Create a preview URL for this specific image
        const reader = new FileReader();
        reader.onloadend = () => {
          const newImagePreviews = [...imagePreviews];
          newImagePreviews[imageIndex] = reader.result as string;
          setImagePreviews(newImagePreviews);
        };
        reader.readAsDataURL(file);
      }
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };

  // Handle image upload to server
  const handleImageUpload = async (file: File, index: number): Promise<string | null> => {
    if (!file) return null;

    setUploadingImage(true);
    try {
      // Validate file size before uploading
      if (file.size > 5 * 1024 * 1024) { // 5MB
        console.error(`Image ${index + 1} is too large: ${Math.round(file.size / 1024 / 1024)}MB`);
        alert(`Image ${index + 1} is too large (${Math.round(file.size / 1024 / 1024)}MB). Maximum size is 5MB.`);
        return null;
      }

      // Validate file type
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!validTypes.includes(file.type)) {
        console.error(`Invalid file type for image ${index + 1}: ${file.type}`);
        alert(`Invalid file type for image ${index + 1}. Please upload a JPEG, PNG, GIF, or WebP image.`);
        return null;
      }

      console.log(`Uploading image ${index + 1}: ${file.name}, size: ${Math.round(file.size / 1024)}KB, type: ${file.type}`);

      const formData = new FormData();
      formData.append('file', file);
      formData.append('category', 'pricing');

      // Add a timeout for the fetch request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout

      try {
        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        // Check for network errors
        if (!response) {
          console.error(`Network error uploading image ${index + 1}`);
          alert(`Network error uploading image ${index + 1}. Please check your connection and try again.`);
          return null;
        }

        let data;
        try {
          data = await response.json();
        } catch (parseError) {
          console.error(`Error parsing response for image ${index + 1}:`, parseError);
          alert(`Error processing server response for image ${index + 1}. The server may be experiencing issues.`);
          return null;
        }

        if (!response.ok) {
          const errorMessage = data?.error || `Failed to upload image ${index + 1} (Status: ${response.status})`;
          console.error(`Upload error for image ${index + 1}:`, errorMessage, data);
          alert(errorMessage);
          return null;
        }

        // Check for warnings
        if (data.warning) {
          console.warn(`Upload warning for image ${index + 1}:`, data.warning);
        }

        if (!data.url) {
          console.error(`Missing URL in response for image ${index + 1}:`, data);
          alert(`Server returned an invalid response for image ${index + 1}. Missing URL in the response.`);
          return null;
        }

        console.log(`Successfully uploaded image ${index + 1}:`, data.url);
        return data.url;
      } catch (error) {
        const fetchError = error as { name?: string; message?: string };
        if (fetchError.name === 'AbortError') {
          console.error(`Upload timeout for image ${index + 1} after 60 seconds`);
          alert(`Upload timeout for image ${index + 1}. The server took too long to respond. Please try again with a smaller image or check your connection.`);
        } else {
          console.error(`Fetch error uploading image ${index + 1}:`, fetchError);
          alert(`Error uploading image ${index + 1}: ${fetchError.message || 'Unknown fetch error'}`);
        }
        return null;
      }
    } catch (error) {
      console.error(`Error uploading image ${index + 1}:`, error);
      alert(`Failed to upload image ${index + 1}. Please try again. Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return null;
    } finally {
      if (index === imageFiles.length - 1) {
        setUploadingImage(false);
      }
    }
  };

  // Upload all images and return their URLs
  const uploadAllImages = async (): Promise<string[]> => {
    setUploadingImage(true);
    const uploadPromises: Promise<string | null>[] = [];

    try {
      // Create upload promises for each image file
      imageFiles.forEach((file, index) => {
        if (file) {
          uploadPromises.push(handleImageUpload(file, index));
        } else {
          uploadPromises.push(Promise.resolve(null));
        }
      });

      // Wait for all uploads to complete
      const results = await Promise.all(uploadPromises);

      // Check if any uploads failed
      const failedUploads = results.filter(result => result === null).length;
      if (failedUploads > 0 && results.some(Boolean)) {
        // Some uploads succeeded, some failed
        console.warn(`${failedUploads} out of ${uploadPromises.length} image uploads failed`);
        alert(`Warning: ${failedUploads} out of ${uploadPromises.length} image uploads failed. You can continue with the successfully uploaded images or try again.`);
      } else if (failedUploads > 0) {
        // All uploads failed
        console.error('All image uploads failed');
        throw new Error('All image uploads failed. Please check your connection and try again.');
      }

      // Filter out null values and return the URLs
      return results.filter(Boolean) as string[];
    } catch (error) {
      console.error('Error uploading images:', error);
      alert(`Error uploading images: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return [];
    } finally {
      setUploadingImage(false);
    }
  };

  // Add a new empty feature field
  const addFeatureField = () => {
    setFormData((prev) => ({
      ...prev,
      features: [...prev.features, '']
    }));
  };

  // Remove a feature field
  const removeFeatureField = (index: number) => {
    const newFeatures = [...formData.features];
    newFeatures.splice(index, 1);
    setFormData((prev) => ({
      ...prev,
      features: newFeatures
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Filter out empty features
      const filteredFeatures = formData.features.filter(feature => feature.trim() !== '');

      // Upload all images that were selected
      const uploadedImageUrls = await uploadAllImages();

      // Map the uploaded URLs to the corresponding imageUrl fields
      const imageUrls = {
        imageUrl: uploadedImageUrls[0] || '',
        imageUrl2: uploadedImageUrls[1] || '',
        imageUrl3: uploadedImageUrls[2] || ''
      };

      const response = await fetch('/api/admin/pricing', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          service: formData.service,
          price: parseInt(formData.price),
          description: formData.description,
          icon: formData.icon || null,
          popular: formData.popular,
          features: filteredFeatures,
          ...imageUrls,
          category: formData.category
        }),
      });

      if (response.ok) {
        // Redirect to pricing list with refresh parameter
        router.push('/admin/pricing?refresh=' + new Date().getTime());
      } else {
        const error = await response.json();
        alert(`Error: ${error.error || 'Failed to create pricing item'}`);
      }
    } catch (error) {
      console.error('Error creating pricing item:', error);
      alert('An error occurred while creating the pricing item');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Link
          href="/admin/pricing"
          className="flex items-center text-sm text-slate-500 hover:text-slate-800"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-1" />
          Back to Pricing
        </Link>
      </div>

      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-slate-800">Add New Pricing Item</h1>
      </div>

      <div className="bg-white rounded-lg shadow-sm p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="service" className="block text-sm font-medium text-gray-700">
              Service Name
            </label>
            <input
              type="text"
              name="service"
              id="service"
              required
              value={formData.service}
              onChange={handleChange}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
              placeholder="e.g. Business Card Design"
            />
          </div>

          <div>
            <label htmlFor="price" className="block text-sm font-medium text-gray-700">
              Price (KSh)
            </label>
            <input
              type="number"
              name="price"
              id="price"
              required
              value={formData.price}
              onChange={handleChange}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
              placeholder="e.g. 500"
            />
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700">
              Description (Optional)
            </label>
            <textarea
              name="description"
              id="description"
              rows={3}
              value={formData.description}
              onChange={handleChange}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
              placeholder="Add any additional information about this service"
            />
          </div>

          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-700">
              Category
            </label>
            <select
              id="category"
              name="category"
              value={formData.category}
              onChange={handleChange}
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm rounded-md"
            >
              <option value="Other">Other</option>
              <option value="Digital Services">Digital Services</option>
              <option value="Printing Services">Printing Services</option>
              <option value="Banners">Banners</option>
              <option value="Office Essentials">Office Essentials</option>
              <option value="Diaries & Notebooks">Diaries & Notebooks</option>
              <option value="Gift Sets">Gift Sets</option>
              <option value="Drinkware">Drinkware</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Product Images (Up to 3 images)
            </label>

            {/* Image Upload Section */}
            <div className="space-y-4">
              {/* Primary Image */}
              <div className="border border-gray-200 rounded-md p-4">
                <div className="flex items-center space-x-4">
                  {imagePreviews[0] ? (
                    <div className="relative">
                      <img
                        src={imagePreviews[0]}
                        alt="Primary image preview"
                        className="h-32 w-32 object-cover rounded-md"
                      />
                      <button
                        type="button"
                        onClick={() => {
                          const newPreviews = [...imagePreviews];
                          newPreviews[0] = '';
                          setImagePreviews(newPreviews);

                          const newFiles = [...imageFiles];
                          newFiles[0] = null;
                          setImageFiles(newFiles);

                          setFormData(prev => ({ ...prev, imageUrl: '' }));
                        }}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 focus:outline-none"
                        aria-label="Remove image"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  ) : (
                    <div className="h-32 w-32 border-2 border-dashed border-gray-300 rounded-md flex items-center justify-center text-gray-400">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                  )}
                  <div className="flex-1">
                    <input
                      type="file"
                      id="image-0"
                      name="image-0"
                      accept="image/*"
                      onChange={handleChange}
                      className="sr-only"
                      ref={(input) => {
                        if (input && !imageFiles[0]) {
                          input.value = '';
                        }
                      }}
                    />
                    <label
                      htmlFor="image-0"
                      className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 cursor-pointer"
                    >
                      {imagePreviews[0] ? 'Change Primary Image' : 'Upload Primary Image'}
                    </label>
                    <p className="mt-1 text-xs text-gray-500">
                      This will be the main image shown for the product
                    </p>
                  </div>
                </div>
              </div>

              {/* Secondary Image */}
              <div className="border border-gray-200 rounded-md p-4">
                <div className="flex items-center space-x-4">
                  {imagePreviews[1] ? (
                    <div className="relative">
                      <img
                        src={imagePreviews[1]}
                        alt="Secondary image preview"
                        className="h-32 w-32 object-cover rounded-md"
                      />
                      <button
                        type="button"
                        onClick={() => {
                          const newPreviews = [...imagePreviews];
                          newPreviews[1] = '';
                          setImagePreviews(newPreviews);

                          const newFiles = [...imageFiles];
                          newFiles[1] = null;
                          setImageFiles(newFiles);

                          setFormData(prev => ({ ...prev, imageUrl2: '' }));
                        }}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 focus:outline-none"
                        aria-label="Remove image"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  ) : (
                    <div className="h-32 w-32 border-2 border-dashed border-gray-300 rounded-md flex items-center justify-center text-gray-400">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                  )}
                  <div className="flex-1">
                    <input
                      type="file"
                      id="image-1"
                      name="image-1"
                      accept="image/*"
                      onChange={handleChange}
                      className="sr-only"
                      ref={(input) => {
                        if (input && !imageFiles[1]) {
                          input.value = '';
                        }
                      }}
                    />
                    <label
                      htmlFor="image-1"
                      className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 cursor-pointer"
                    >
                      {imagePreviews[1] ? 'Change Secondary Image' : 'Upload Secondary Image (Optional)'}
                    </label>
                    <p className="mt-1 text-xs text-gray-500">
                      Additional product view or detail
                    </p>
                  </div>
                </div>
              </div>

              {/* Tertiary Image */}
              <div className="border border-gray-200 rounded-md p-4">
                <div className="flex items-center space-x-4">
                  {imagePreviews[2] ? (
                    <div className="relative">
                      <img
                        src={imagePreviews[2]}
                        alt="Tertiary image preview"
                        className="h-32 w-32 object-cover rounded-md"
                      />
                      <button
                        type="button"
                        onClick={() => {
                          const newPreviews = [...imagePreviews];
                          newPreviews[2] = '';
                          setImagePreviews(newPreviews);

                          const newFiles = [...imageFiles];
                          newFiles[2] = null;
                          setImageFiles(newFiles);

                          setFormData(prev => ({ ...prev, imageUrl3: '' }));
                        }}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 focus:outline-none"
                        aria-label="Remove image"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  ) : (
                    <div className="h-32 w-32 border-2 border-dashed border-gray-300 rounded-md flex items-center justify-center text-gray-400">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                  )}
                  <div className="flex-1">
                    <input
                      type="file"
                      id="image-2"
                      name="image-2"
                      accept="image/*"
                      onChange={handleChange}
                      className="sr-only"
                      ref={(input) => {
                        if (input && !imageFiles[2]) {
                          input.value = '';
                        }
                      }}
                    />
                    <label
                      htmlFor="image-2"
                      className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 cursor-pointer"
                    >
                      {imagePreviews[2] ? 'Change Tertiary Image' : 'Upload Tertiary Image (Optional)'}
                    </label>
                    <p className="mt-1 text-xs text-gray-500">
                      Additional product view or detail
                    </p>
                  </div>
                </div>
              </div>

              <p className="text-xs text-gray-500 mt-2">
                All images: JPG, PNG or GIF up to 5MB each. Images will be uploaded to S3 storage.
              </p>
            </div>
          </div>

          <div>
            <label htmlFor="icon" className="block text-sm font-medium text-gray-700">
              Icon (Optional)
            </label>
            <div className="mt-1 flex rounded-md shadow-sm">
              <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 sm:text-sm">
                fa-
              </span>
              <input
                type="text"
                name="icon"
                id="icon"
                value={formData.icon}
                onChange={handleChange}
                className="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-r-md border border-gray-300 focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                placeholder="e.g. id-card, file, book-open"
              />
            </div>
            <p className="mt-1 text-sm text-gray-500">
              Enter a FontAwesome icon name without the "fa-" prefix. Used as fallback when no image is provided.
            </p>
          </div>

          <div className="flex items-center">
            <input
              id="popular"
              name="popular"
              type="checkbox"
              checked={formData.popular}
              onChange={handleChange}
              className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
            />
            <label htmlFor="popular" className="ml-2 block text-sm text-gray-700">
              Mark as popular service
            </label>
          </div>

          <div>
            <div className="flex justify-between items-center mb-2">
              <label className="block text-sm font-medium text-gray-700">
                Service Features
              </label>
              <button
                type="button"
                onClick={addFeatureField}
                className="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-orange-700 bg-orange-100 hover:bg-orange-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
              >
                Add Feature
              </button>
            </div>

            <div className="space-y-2">
              {formData.features.map((feature, index) => (
                <div key={index} className="flex items-center">
                  <input
                    type="text"
                    name={`feature-${index}`}
                    value={feature}
                    onChange={handleChange}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                    placeholder={`Feature ${index + 1}`}
                  />
                  {formData.features.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeFeatureField(index)}
                      className="ml-2 inline-flex items-center p-1.5 border border-transparent rounded-full text-red-600 hover:bg-red-50 focus:outline-none"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  )}
                </div>
              ))}
            </div>
            <p className="mt-1 text-sm text-gray-500">
              Add features that will be displayed on the pricing card. Leave empty to use default features.
            </p>
          </div>

          <div className="flex justify-end">
            <Link
              href="/admin/pricing"
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 mr-3"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={loading}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            >
              {loading ? 'Saving...' : 'Save Pricing Item'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}