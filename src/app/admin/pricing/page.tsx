'use client';

import { useState, useEffect } from 'react';
import {
  ArrowPathIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  TrashIcon,
  XMarkIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';

interface PricingItem {
  id: string;
  service: string;
  price: number;
  description: string;
  features?: string[];
  icon?: string;
  popular?: boolean;
  imageUrl?: string;
  imageUrl2?: string;
  imageUrl3?: string;
  category?: string;
  createdAt: string;
  updatedAt: string;
}

export default function PricingPage() {
  const [loading, setLoading] = useState(false);
  const [pricingItems, setPricingItems] = useState<PricingItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<PricingItem[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  const [categories, setCategories] = useState<string[]>(['All']);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [bulkDeleteLoading, setBulkDeleteLoading] = useState(false);

  // Get search params to detect when coming back from edit page
  const searchParams = useSearchParams();
  const refreshParam = searchParams.get('refresh');

  const fetchPricing = async () => {
    setLoading(true);
    try {
      // Add a timestamp to bust the cache
      const timestamp = new Date().getTime();
      const response = await fetch(`/api/admin/pricing?t=${timestamp}`);
      if (response.ok) {
        const data = await response.json();
        setPricingItems(data);
        setFilteredItems(data);

        // Extract unique categories
        const uniqueCategories = Array.from(
          new Set(data.map((item: PricingItem) => item.category || 'Other'))
        ).sort();

        // Add 'All' category at the beginning
        setCategories(['All', ...uniqueCategories]);
      }
    } catch (error) {
      console.error('Error fetching pricing items:', error);
    } finally {
      setLoading(false);
    }
  };

  // Filter items when search term or selected category changes
  useEffect(() => {
    if (!pricingItems.length) return;

    let filtered = [...pricingItems];

    // Filter by category first (if not 'All')
    if (selectedCategory !== 'All') {
      filtered = filtered.filter(item =>
        (item.category || 'Other') === selectedCategory
      );
    }

    // Then filter by search term
    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase().trim();
      filtered = filtered.filter(item =>
        item.service.toLowerCase().includes(term) ||
        (item.description && item.description.toLowerCase().includes(term)) ||
        item.price.toString().includes(term) ||
        (item.category && item.category.toLowerCase().includes(term))
      );
    }

    setFilteredItems(filtered);
  }, [searchTerm, selectedCategory, pricingItems]);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Handle bulk delete
  const handleBulkDelete = async () => {
    if (selectedItems.length === 0) return;

    if (window.confirm(`Are you sure you want to delete ${selectedItems.length} selected pricing items?`)) {
      setBulkDeleteLoading(true);
      try {
        const response = await fetch('/api/admin/pricing/bulk-delete', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ ids: selectedItems }),
        });

        if (response.ok) {
          const result = await response.json();
          alert(`Successfully deleted ${result.count} pricing items`);
          setSelectedItems([]);
          fetchPricing();
        } else {
          const error = await response.json();
          alert(`Error: ${error.error || 'Failed to delete pricing items'}`);
        }
      } catch (error) {
        console.error('Error bulk deleting pricing items:', error);
        alert('An error occurred while deleting pricing items');
      } finally {
        setBulkDeleteLoading(false);
      }
    }
  };

  // Toggle item selection
  const toggleItemSelection = (id: string) => {
    setSelectedItems(prev =>
      prev.includes(id)
        ? prev.filter(itemId => itemId !== id)
        : [...prev, id]
    );
  };

  // Toggle all items selection
  const toggleAllSelection = () => {
    if (selectedItems.length === filteredItems.length) {
      // If all are selected, deselect all
      setSelectedItems([]);
    } else {
      // Otherwise, select all filtered items
      setSelectedItems(filteredItems.map(item => item.id));
    }
  };

  useEffect(() => {
    fetchPricing();
    // When refreshParam changes, refetch pricing items
  }, [refreshParam]);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-slate-800">Pricing</h1>
        <div className="flex space-x-2">
          <button
            onClick={fetchPricing}
            className="p-2 rounded-lg text-slate-500 hover:text-slate-700 hover:bg-slate-100"
            disabled={loading}
            title="Refresh"
          >
            <ArrowPathIcon className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
          </button>
          <Link
            href="/admin/pricing/new"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600"
          >
            <PlusIcon className="h-4 w-4 mr-1" />
            Add Pricing Item
          </Link>
        </div>
      </div>

      {/* Search and Filter Section */}
      <div className="bg-white rounded-lg shadow-sm p-4">
        <div className="flex flex-col md:flex-row gap-4 mb-4">
          {/* Search Bar */}
          <div className="relative flex-grow">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              value={searchTerm}
              onChange={handleSearchChange}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
              placeholder="Search by name, description, price..."
            />
            {searchTerm && (
              <button
                onClick={() => setSearchTerm('')}
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                <XMarkIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
              </button>
            )}
          </div>

          {/* Category Filter */}
          <div className="relative min-w-[200px]">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FunnelIcon className="h-5 w-5 text-gray-400" />
            </div>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
            >
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category === 'All' ? 'All Categories' : category}
                </option>
              ))}
            </select>
          </div>

          {/* Bulk Delete Button */}
          {selectedItems.length > 0 && (
            <button
              onClick={handleBulkDelete}
              disabled={bulkDeleteLoading}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-500 hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              <TrashIcon className="h-4 w-4 mr-1" />
              Delete Selected ({selectedItems.length})
            </button>
          )}
        </div>

        {/* Filter Status */}
        <div className="text-sm text-gray-500">
          {filteredItems.length === 0 && pricingItems.length > 0 ? (
            <p>No items match your search criteria. <button onClick={() => { setSearchTerm(''); setSelectedCategory('All'); }} className="text-orange-500 hover:text-orange-600">Clear filters</button></p>
          ) : (
            <p>Showing {filteredItems.length} of {pricingItems.length} pricing items</p>
          )}
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        {pricingItems.length === 0 ? (
          <div className="p-8 text-center">
            <div className="mx-auto h-12 w-12 text-slate-400 mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M12 6v12m-3-2.818l.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-slate-800 mb-1">No pricing items found</h3>
            <p className="text-slate-500 mb-4">Get started by creating your first pricing item</p>
            <Link
              href="/admin/pricing/new"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600"
            >
              Create your first pricing item
            </Link>
          </div>
        ) : filteredItems.length === 0 ? (
          <div className="p-8 text-center">
            <div className="mx-auto h-12 w-12 text-slate-400 mb-4">
              <MagnifyingGlassIcon />
            </div>
            <h3 className="text-lg font-medium text-slate-800 mb-1">No matching items</h3>
            <p className="text-slate-500 mb-4">Try adjusting your search or filter to find what you're looking for</p>
            <button
              onClick={() => { setSearchTerm(''); setSelectedCategory('All'); }}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600"
            >
              Clear Filters
            </button>
          </div>
        ) : (
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th
                  scope="col"
                  className="px-3 py-3 text-left text-xs font-medium text-gray-500"
                >
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                      checked={selectedItems.length === filteredItems.length && filteredItems.length > 0}
                      onChange={toggleAllSelection}
                    />
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Service
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Price (KSh)
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Category
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Image
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Description
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredItems.map((item) => (
                <tr key={item.id} className={selectedItems.includes(item.id) ? 'bg-orange-50' : ''}>
                  <td className="px-3 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                        checked={selectedItems.includes(item.id)}
                        onChange={() => toggleItemSelection(item.id)}
                      />
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {item.service}
                      {item.popular && (
                        <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                          Popular
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">{item.price.toLocaleString()}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">
                      {item.category || 'Other'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {item.imageUrl || item.imageUrl2 || item.imageUrl3 ? (
                      <div className="flex space-x-1">
                        {item.imageUrl && (
                          <div className="h-10 w-10 rounded overflow-hidden bg-gray-100">
                            <img
                              src={item.imageUrl}
                              alt={`${item.service} - primary`}
                              className="h-full w-full object-cover"
                              title="Primary image"
                            />
                          </div>
                        )}
                        {item.imageUrl2 && (
                          <div className="h-10 w-10 rounded overflow-hidden bg-gray-100">
                            <img
                              src={item.imageUrl2}
                              alt={`${item.service} - secondary`}
                              className="h-full w-full object-cover"
                              title="Secondary image"
                            />
                          </div>
                        )}
                        {item.imageUrl3 && (
                          <div className="h-10 w-10 rounded overflow-hidden bg-gray-100">
                            <img
                              src={item.imageUrl3}
                              alt={`${item.service} - tertiary`}
                              className="h-full w-full object-cover"
                              title="Tertiary image"
                            />
                          </div>
                        )}
                      </div>
                    ) : (
                      <span className="text-gray-400 text-sm">No images</span>
                    )}
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-500 truncate max-w-xs">
                      {item.description || '-'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <Link
                      href={`/admin/pricing/${item.id}/edit`}
                      className="text-orange-500 hover:text-orange-700 mr-4"
                    >
                      Edit
                    </Link>
                    <button
                      className="text-red-500 hover:text-red-700"
                      onClick={async () => {
                        if (window.confirm(`Are you sure you want to delete the pricing item "${item.service}"?`)) {
                          setLoading(true);
                          try {
                            const response = await fetch(`/api/admin/pricing/${item.id}`, {
                              method: 'DELETE',
                            });

                            if (response.ok) {
                              // Remove from selected items if it was selected
                              if (selectedItems.includes(item.id)) {
                                setSelectedItems(prev => prev.filter(id => id !== item.id));
                              }
                              setPricingItems(prev => prev.filter(c => c.id !== item.id));
                              setFilteredItems(prev => prev.filter(c => c.id !== item.id));
                              alert('Pricing item deleted successfully');
                            } else {
                              alert('Failed to delete pricing item');
                            }
                          } catch (error) {
                            console.error('Error deleting pricing item:', error);
                            alert('An error occurred while deleting the pricing item');
                          } finally {
                            setLoading(false);
                          }
                        }
                      }}
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>
    </div>
  );
}