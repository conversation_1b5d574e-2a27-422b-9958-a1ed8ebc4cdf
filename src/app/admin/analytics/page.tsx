'use client';

import { ChartBarIcon } from '@heroicons/react/24/outline';
import OverviewStats from '@/components/admin/analytics/OverviewStats';
import PageViewsChart from '@/components/admin/analytics/PageViewsChart';
import TrafficSourcesChart from '@/components/admin/analytics/TrafficSourcesChart';
import TopPagesTable from '@/components/admin/analytics/TopPagesTable';
import Device<PERSON>hart from '@/components/admin/analytics/DeviceChart';
import CountryChart from '@/components/admin/analytics/CountryChart';

export default function AnalyticsDashboard() {
  return (
    <div className="space-y-8">
      <div className="flex items-center">
        <div className="p-2 bg-blue-50 text-blue-500 rounded-lg mr-3">
          <ChartBarIcon className="h-6 w-6" />
        </div>
        <h1 className="text-2xl sm:text-3xl font-semibold text-slate-800">Analytics Dashboard</h1>
      </div>

      {/* Overview Stats */}
      <OverviewStats />

      {/* Page Views Chart */}
      <PageViewsChart />

      {/* Two Column Layout for Traffic Sources and Devices */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <TrafficSourcesChart />
        <DeviceChart />
      </div>

      {/* Top Pages Table */}
      <TopPagesTable />

      {/* Country Chart */}
      <CountryChart />
    </div>
  );
}
