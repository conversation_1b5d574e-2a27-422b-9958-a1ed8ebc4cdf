import { PrismaClient } from '@prisma/client';
import prisma from '@/lib/prisma';

export interface StorageConfig {
  id: string;
  provider: 'S3' | 'LOCAL' | 'LINODE' | 'CLOUDINARY';
  region: string;
  endpoint: string;
  bucketName: string;
  accessKeyId: string;
  secretAccessKey: string;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface StorageConfigInput {
  provider: 'S3' | 'LOCAL' | 'LINODE' | 'CLOUDINARY';
  region: string;
  endpoint: string;
  bucketName: string;
  accessKeyId: string;
  secretAccessKey: string;
  isDefault: boolean;
}

/**
 * Get the default storage configuration
 */
export async function getDefaultStorageConfig(): Promise<StorageConfig | null> {
  try {
    console.log('Fetching default storage configuration...');

    // Check if the StorageConfig model exists in the Prisma client
    if (!prisma.storageConfig) {
      console.error('StorageConfig model not found in Prisma client');
      throw new Error('StorageConfig model not found in Prisma client');
    }

    // @ts-ignore - Using the StorageConfig table that's defined in our schema
    const config = await prisma.storageConfig.findFirst({
      where: { isDefault: true },
    });

    if (config) {
      console.log('Found default storage configuration:', config.id);
    } else {
      console.log('No default storage configuration found in database');
    }

    return config;
  } catch (error) {
    console.error('Error fetching default storage config:', error);

    // Fallback to environment variables if DB lookup fails
    if (process.env.NEXT_PUBLIC_S3_BUCKET) {
      console.log('Falling back to environment variables for storage configuration');
      return {
        id: 'env-default',
        provider: 'S3',
        region: process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1',
        endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT || '',
        bucketName: process.env.NEXT_PUBLIC_S3_BUCKET || 'mocky',
        accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY || '',
        secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY || '',
        isDefault: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
    }

    console.error('No fallback storage configuration available');
    return null;
  }
}

/**
 * Get all storage configurations
 */
export async function getAllStorageConfigs(): Promise<StorageConfig[]> {
  try {
    console.log('Fetching all storage configurations...');

    // Check if the StorageConfig model exists in the Prisma client
    if (!prisma.storageConfig) {
      console.error('StorageConfig model not found in Prisma client');
      throw new Error('StorageConfig model not found in Prisma client');
    }

    // @ts-ignore - Using the StorageConfig table that's defined in our schema
    const configs = await prisma.storageConfig.findMany({
      orderBy: { createdAt: 'desc' },
    });

    console.log(`Found ${configs.length} storage configurations`);
    return configs;
  } catch (error) {
    console.error('Error fetching storage configs:', error);
    return [];
  }
}

/**
 * Create a new storage configuration
 */
export async function createStorageConfig(data: StorageConfigInput): Promise<StorageConfig> {
  try {
    // If this config is set as default, unset any existing defaults
    if (data.isDefault) {
      // @ts-ignore - Using the StorageConfig table that's defined in our schema
      await prisma.storageConfig.updateMany({
        where: { isDefault: true },
        data: { isDefault: false },
      });
    }

    // @ts-ignore - Using the StorageConfig table that's defined in our schema
    const config = await prisma.storageConfig.create({
      data,
    });

    return config;
  } catch (error) {
    console.error('Error creating storage config:', error);
    throw error;
  }
}

/**
 * Update an existing storage configuration
 */
export async function updateStorageConfig(id: string, data: Partial<StorageConfigInput>): Promise<StorageConfig> {
  try {
    // If this config is set as default, unset any existing defaults
    if (data.isDefault) {
      // @ts-ignore - Using the StorageConfig table that's defined in our schema
      await prisma.storageConfig.updateMany({
        where: {
          isDefault: true,
          id: { not: id }
        },
        data: { isDefault: false },
      });
    }

    // @ts-ignore - Using the StorageConfig table that's defined in our schema
    const config = await prisma.storageConfig.update({
      where: { id },
      data,
    });

    return config;
  } catch (error) {
    console.error('Error updating storage config:', error);
    throw error;
  }
}

/**
 * Delete a storage configuration
 */
export async function deleteStorageConfig(id: string): Promise<void> {
  try {
    // Check if this is the default config
    // @ts-ignore - Using the StorageConfig table that's defined in our schema
    const config = await prisma.storageConfig.findUnique({
      where: { id },
    });

    if (config?.isDefault) {
      throw new Error('Cannot delete the default storage configuration');
    }

    // @ts-ignore - Using the StorageConfig table that's defined in our schema
    await prisma.storageConfig.delete({
      where: { id },
    });
  } catch (error) {
    console.error('Error deleting storage config:', error);
    throw error;
  }
}