/** @type {import('next').NextConfig} */
const nextConfig = {
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  reactStrictMode: true,
  output: 'standalone',
  images: {
    remotePatterns: [
      {
        hostname: 'mocky.co.ke',
        protocol: 'https',
        pathname: '/**',
      },
      {
        hostname: 'mocky.co.ke',
        protocol: 'http',
        pathname: '/**',
      },
      {
        hostname: 'localhost',
        protocol: 'http',
        pathname: '/**',
      },
      {
        hostname: 'fr-par-1.linodeobjects.com',
        protocol: 'https',
        pathname: '/**',
      },
      {
        // Add a wildcard pattern to catch any S3 compatible storage
        protocol: 'https',
        hostname: '**.linodeobjects.com',
        pathname: '/**',
      },
      {
        // Add a pattern for any subdomain of linodeobjects.com
        protocol: 'http',
        hostname: '**.linodeobjects.com',
        pathname: '/**',
      },
      {
        // Add a pattern for any AWS S3 bucket
        protocol: 'https',
        hostname: '**.amazonaws.com',
        pathname: '/**',
      },
    ],
    formats: ['image/avif', 'image/webp'],
    minimumCacheTTL: 60 * 60 * 24 * 30, // 30 days
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    unoptimized: process.env.NODE_ENV === 'development' // Skip optimization in development
  },
  typescript: {
    // Ignore type checking errors in production
    ignoreBuildErrors: process.env.NODE_ENV === 'production',
  },
  eslint: {
    // Ignore ESLint errors in production build
    ignoreDuringBuilds: true,
  },
  // Move to top level as per Next.js 15.2.2
  outputFileTracingExcludes: {
    '*': [
      'node_modules/@swc/core-linux-x64-gnu',
      'node_modules/@swc/core-linux-x64-musl',
      'node_modules/@esbuild/linux-x64',
    ],
  },
  // Remove experimental.appDir since App Router is now the default
  // Remove future.webpack5 since it's the default now
  async headers() {
    return [
      {
        source: '/:all*(svg|jpg|png|webp|avif)',
        locale: false,
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      {
        source: '/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=3600, s-maxage=86400, stale-while-revalidate=43200',
          },
        ],
      },
      {
        source: '/images/:path*',
        headers: [
          {
            key: 'Cache-Control',
            // Much longer cache for images
            value: 'public, max-age=86400, s-maxage=31536000, stale-while-revalidate=86400',
          },
        ],
      },
      {
        source: '/_next/image',
        headers: [
          {
            key: 'Cache-Control',
            // Image optimization cache
            value: 'public, max-age=60, s-maxage=31536000, stale-while-revalidate=31536000',
          },
        ],
      },
      {
        source: '/_next/static/:path*',
        headers: [
          {
            key: 'Cache-Control',
            // Very long cache for static assets
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      {
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET,POST,PUT,DELETE' },
        ],
      },
    ];
  },
  // Add better error handling
  onDemandEntries: {
    // Keep in memory for longer during development
    maxInactiveAge: 25 * 1000,
    // Show more detailed webpack errors
    pagesBufferLength: 5,
  },
  // Add these for CSS support
  webpack: (config, { isServer }) => {
    // Disable side effects filtering to prevent any module loss
    if (config.optimization && config.optimization.sideEffects === true) {
      config.optimization.sideEffects = false;
    }

    // Add more optimization for production builds
    if (process.env.NODE_ENV === 'production') {
      // Ensure terser is used for minification
      if (config.optimization && config.optimization.minimizer) {
        const TerserPlugin = require('terser-webpack-plugin');
        config.optimization.minimizer.push(
          new TerserPlugin({
            terserOptions: {
              compress: {
                drop_console: true,
                drop_debugger: true,
              },
              output: {
                comments: false,
              },
            },
            extractComments: false,
          })
        );
      }
    }

    // Add a fallback for node modules that depend on window
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      };
    }

    return config;
  },
  sassOptions: {
    includePaths: ['./src'],
  },
  // Add redirects for old image paths and renamed pages
  async redirects() {
    return [
      {
        source: '/images/logos/:path*',
        destination: '/images/portfolio/logos/:path*',
        permanent: true,
      },
      {
        source: '/pricing',
        destination: '/catalogue',
        permanent: true,
      },
      {
        source: '/pricing/:path*',
        destination: '/catalogue/:path*',
        permanent: true,
      },
      // Note: Next.js redirects can only handle paths, not protocols or domains
      // Protocol and domain redirects should be handled by Nginx
    ]
  },

  // Optimize output
  compress: true,
  poweredByHeader: false,
  generateEtags: true,
  productionBrowserSourceMaps: false, // Disable source maps in production for performance
  transpilePackages: ['@/components/ContactFormWrapper', '@/components/ContactForm'],
  // Enable the experimental transpilation of specific modules
  experimental: {
    serverActions: {
      allowedOrigins: ['localhost:3000', 'mocky.co.ke'],
    }
  },
};

module.exports = nextConfig;