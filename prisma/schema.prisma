generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model BlogPost {
  title       String    @db.VarChar(255)
  slug        String    @unique @db.VarChar(255)
  content     String
  excerpt     String?
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @default(now()) @updatedAt @map("updated_at")
  publishedAt DateTime? @map("published_at")
  author      String?   @db.VarChar(100)
  category    String?   @db.VarChar(100)
  status      String    @default("draft") @db.VarChar(20)
  tags        String[]  @default([])
  id          Int       @id @default(autoincrement())

  @@map("blog_posts")
}

model Category {
  name        String   @db.VarChar(100)
  slug        String   @unique @db.VarChar(100)
  description String?
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")
  id          Int      @id @default(autoincrement())

  @@map("categories")
}

model WebsitePortfolio {
  id          String   @id @default(uuid())
  title       String   @db.VarChar(255)
  description String?
  url         String
  imageSrc    String
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")
  category    String   @db.VarChar(100)
  featured    Boolean  @default(false)

  @@map("website_portfolio")
}

model Pricing {
  description String?
  price       Int
  features    String[] @default([])
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")
  icon        String?  @db.VarChar(100)
  popular     Boolean? @default(false)
  service     String   @db.VarChar(255)
  id          Int      @id @default(autoincrement())
  category    String?  @default("Other") @db.VarChar(100)
  imageUrl    String?
  imageUrl2   String?
  imageUrl3   String?

  @@map("pricing")
}

model technology_items {
  id                Int               @id @default(autoincrement())
  name              String            @db.VarChar(100)
  icon              String            @db.VarChar(50)
  level             String            @db.VarChar(20)
  experience        String            @db.VarChar(50)
  stackId           Int
  created_at        DateTime          @default(now())
  updated_at        DateTime          @default(now())
  technology_stacks technology_stacks @relation(fields: [stackId], references: [id])
}

model technology_stacks {
  id               Int                @id @default(autoincrement())
  category         String             @db.VarChar(100)
  icon             String             @db.VarChar(50)
  description      String
  bgColor          String             @db.VarChar(100)
  textColor        String             @db.VarChar(100)
  created_at       DateTime           @default(now())
  updated_at       DateTime           @default(now())
  technology_items technology_items[]
}

model StorageConfig {
  id              String   @id @default(uuid())
  provider        String   @db.VarChar(50)
  region          String   @db.VarChar(100)
  endpoint        String   @db.VarChar(255)
  bucketName      String   @db.VarChar(100)
  accessKeyId     String   @db.VarChar(255)
  secretAccessKey String   @db.VarChar(255)
  isDefault       Boolean  @default(false)
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("storage_config")
}

model SiteSettings {
  id                String   @id @default(uuid())
  siteName          String   @default("Mocky")
  siteDescription   String?  @db.VarChar(255)
  contactEmail      String?  @db.VarChar(100)
  address           String?  @db.VarChar(255)
  facebookUrl       String?  @db.VarChar(255)
  twitterUrl        String?  @db.VarChar(255)
  instagramUrl      String?  @db.VarChar(255)
  linkedinUrl       String?  @db.VarChar(255)
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")
  googleAnalyticsId String?  @db.VarChar(50)
  metaDescription   String?
  metaTitle         String?  @db.VarChar(255)
  phoneNumber       String?  @db.VarChar(50)
  tiktokUrl         String?  @db.VarChar(255)

  @@map("site_settings")
}

model TeamMember {
  id           String   @id @default(uuid())
  name         String   @db.VarChar(100)
  role         String   @db.VarChar(100)
  bio          String
  imageSrc     String
  order        Int      @default(0)
  linkedinUrl  String?  @db.VarChar(255)
  twitterUrl   String?  @db.VarChar(255)
  githubUrl    String?  @db.VarChar(255)
  emailAddress String?  @db.VarChar(100)
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("team_members")
}

model DatabaseBackup {
  id          String   @id @default(uuid())
  filename    String   @db.VarChar(255)
  description String?
  size        Int      @default(0)
  path        String   @db.VarChar(255)
  type        String   @db.VarChar(50)
  status      String   @default("completed") @db.VarChar(50)
  createdBy   String?  @db.VarChar(100)
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("database_backups")
}

model ScheduledBlogPost {
  id             String   @id @default(uuid())
  category       String?  @db.VarChar(100)
  tone           String?  @db.VarChar(50)
  length         String?  @db.VarChar(20)
  targetAudience String?  @db.VarChar(255)
  scheduledDate  DateTime
  status         String   @default("pending") @db.VarChar(20)
  blogPostId     String?  @db.VarChar(255)
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("scheduled_blog_posts")
}

model Transaction {
  id              String    @id @default(uuid())
  transactionId   String    @unique @db.VarChar(50)
  amount          Decimal   @db.Decimal(10, 2)
  customerName    String    @db.VarChar(100)
  phoneNumber     String    @db.VarChar(20)
  transactionDate DateTime
  rawMessage      String
  status          String    @default("pending") @db.VarChar(20)
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @default(now()) @updatedAt @map("updated_at")
  processedAt     DateTime? @map("processed_at")
  receipt         Receipt?

  @@map("transactions")
}

model Service {
  id           String        @id @default(uuid())
  name         String        @db.VarChar(100)
  description  String?
  price        Decimal       @db.Decimal(10, 2)
  category     String        @db.VarChar(50)
  createdAt    DateTime      @default(now()) @map("created_at")
  updatedAt    DateTime      @default(now()) @updatedAt @map("updated_at")
  invoiceItems InvoiceItem[]
  quoteItems   QuoteItem[]
  receiptItems ReceiptItem[]

  @@map("services")
}

model Receipt {
  id            String        @id @default(uuid())
  receiptNumber String        @unique @db.VarChar(50)
  totalAmount   Decimal       @db.Decimal(10, 2)
  amountPaid    Decimal       @db.Decimal(10, 2)
  balance       Decimal       @db.Decimal(10, 2)
  customerName  String        @db.VarChar(100)
  phoneNumber   String        @db.VarChar(20)
  email         String?       @db.VarChar(100)
  status        String        @default("issued") @db.VarChar(20)
  notes         String?
  createdAt     DateTime      @default(now()) @map("created_at")
  updatedAt     DateTime      @default(now()) @updatedAt @map("updated_at")
  issuedAt      DateTime      @default(now()) @map("issued_at")
  paidAt        DateTime?     @map("paid_at")
  transactionId String        @unique
  pdfUrl        String?       @db.VarChar(255)
  items         ReceiptItem[]
  transaction   Transaction   @relation(fields: [transactionId], references: [id], onDelete: Cascade)

  @@map("receipts")
}

model ReceiptItem {
  id          String   @id @default(uuid())
  quantity    Int      @default(1)
  unitPrice   Decimal  @db.Decimal(10, 2)
  totalPrice  Decimal  @db.Decimal(10, 2)
  description String?
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")
  receiptId   String
  serviceId   String
  receipt     Receipt  @relation(fields: [receiptId], references: [id], onDelete: Cascade)
  service     Service  @relation(fields: [serviceId], references: [id])

  @@map("receipt_items")
}

model Invoice {
  id            String        @id @default(uuid())
  invoiceNumber String        @unique @db.VarChar(50)
  totalAmount   Decimal       @db.Decimal(10, 2)
  amountPaid    Decimal       @default(0) @db.Decimal(10, 2)
  balance       Decimal       @db.Decimal(10, 2)
  customerName  String        @db.VarChar(100)
  phoneNumber   String        @db.VarChar(20)
  email         String?       @db.VarChar(100)
  status        String        @default("pending") @db.VarChar(20)
  notes         String?
  createdAt     DateTime      @default(now()) @map("created_at")
  updatedAt     DateTime      @default(now()) @updatedAt @map("updated_at")
  issuedAt      DateTime      @default(now()) @map("issued_at")
  dueDate       DateTime
  paidAt        DateTime?     @map("paid_at")
  pdfUrl        String?       @db.VarChar(255)
  quoteId       String?       @unique
  items         InvoiceItem[]
  quote         Quote?        @relation(fields: [quoteId], references: [id])

  @@map("invoices")
}

model InvoiceItem {
  id          String   @id @default(uuid())
  quantity    Int      @default(1)
  unitPrice   Decimal  @db.Decimal(10, 2)
  totalPrice  Decimal  @db.Decimal(10, 2)
  description String?
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")
  invoiceId   String
  serviceId   String
  invoice     Invoice  @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  service     Service  @relation(fields: [serviceId], references: [id])

  @@map("invoice_items")
}

model Quote {
  id           String      @id @default(uuid())
  quoteNumber  String      @unique @db.VarChar(50)
  totalAmount  Decimal     @db.Decimal(10, 2)
  customerName String      @db.VarChar(100)
  phoneNumber  String      @db.VarChar(20)
  email        String?     @db.VarChar(100)
  status       String      @default("pending") @db.VarChar(20)
  notes        String?
  createdAt    DateTime    @default(now()) @map("created_at")
  updatedAt    DateTime    @default(now()) @updatedAt @map("updated_at")
  issuedAt     DateTime    @default(now()) @map("issued_at")
  validUntil   DateTime
  pdfUrl       String?     @db.VarChar(255)
  invoice      Invoice?
  items        QuoteItem[]

  @@map("quotes")
}

model QuoteItem {
  id          String   @id @default(uuid())
  quantity    Int      @default(1)
  unitPrice   Decimal  @db.Decimal(10, 2)
  totalPrice  Decimal  @db.Decimal(10, 2)
  description String?
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")
  quoteId     String
  serviceId   String
  quote       Quote    @relation(fields: [quoteId], references: [id], onDelete: Cascade)
  service     Service  @relation(fields: [serviceId], references: [id])

  @@map("quote_items")
}

model User {
  id           String        @id @default(uuid())
  username     String        @unique @db.VarChar(50)
  email        String        @unique @db.VarChar(100)
  name         String?       @db.VarChar(100)
  passwordHash String        @db.VarChar(255)
  active       Boolean       @default(true)
  roleId       String
  lastLogin    DateTime?
  createdAt    DateTime      @default(now()) @map("created_at")
  updatedAt    DateTime      @default(now()) @updatedAt @map("updated_at")
  activityLogs ActivityLog[]
  assignedLeads Lead[]
  role         Role          @relation(fields: [roleId], references: [id])

  @@map("users")
}

model Role {
  id          String   @id @default(uuid())
  name        String   @unique @db.VarChar(50)
  description String?
  permissions String[] @default([])
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")
  users       User[]

  @@map("roles")
}

model ActivityLog {
  id           String   @id @default(uuid())
  userId       String
  action       String   @db.VarChar(100)
  details      String?
  ipAddress    String?  @db.VarChar(50)
  userAgent    String?
  resourceType String?  @db.VarChar(50)
  resourceId   String?
  createdAt    DateTime @default(now()) @map("created_at")
  user         User     @relation(fields: [userId], references: [id])

  @@map("activity_logs")
}

model SiteScript {
  id          String   @id @default(uuid())
  name        String   @db.VarChar(100)
  description String?
  scriptType  String   @db.VarChar(50)
  content     String
  isActive    Boolean  @default(true)
  position    Int      @default(0)
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("site_scripts")
}

model Lead {
  id                String         @id @default(uuid())
  name              String         @db.VarChar(100)
  email             String         @db.VarChar(100)
  phone             String?        @db.VarChar(20)
  company           String?        @db.VarChar(100)
  source            String?        @db.VarChar(50)
  status            String         @default("new") @db.VarChar(20)
  score             Int            @default(0)
  notes             String?
  assignedToUserId  String?
  lastContactedAt   DateTime?
  nextFollowUpDate  DateTime?
  createdAt         DateTime       @default(now()) @map("created_at")
  updatedAt         DateTime       @default(now()) @updatedAt @map("updated_at")
  interactions      Interaction[]
  assignedTo        User?          @relation(fields: [assignedToUserId], references: [id])

  @@map("leads")
}

model Interaction {
  id          String   @id @default(uuid())
  leadId      String
  type        String   @db.VarChar(50)
  details     String?
  createdAt   DateTime @default(now()) @map("created_at")
  createdBy   String?
  lead        Lead     @relation(fields: [leadId], references: [id], onDelete: Cascade)

  @@map("interactions")
}

model EventTracking {
  id          String   @id @default(uuid())
  eventName   String   @db.VarChar(100)
  eventType   String   @db.VarChar(50)
  url         String?  @db.VarChar(255)
  userAgent   String?
  ipAddress   String?  @db.VarChar(50)
  sessionId   String?  @db.VarChar(100)
  userId      String?
  leadId      String?
  metadata    Json?
  createdAt   DateTime @default(now()) @map("created_at")

  @@map("event_tracking")
}

model SeoPage {
  id                String              @id @default(uuid())
  url               String              @unique @db.VarChar(255)
  title             String              @db.VarChar(255)
  description       String?
  keywords          String[]            @default([])
  lastScanned       DateTime?
  healthScore       Int                 @default(0)
  status            String              @default("pending") @db.VarChar(20)
  createdAt         DateTime            @default(now()) @map("created_at")
  updatedAt         DateTime            @default(now()) @updatedAt @map("updated_at")
  seoIssues         SeoIssue[]
  seoKeywords       SeoKeyword[]
  seoMetaTags       SeoMetaTag[]
  seoStructuredData SeoStructuredData[]
  brokenLinks       BrokenLink[]

  @@map("seo_pages")
}

model SeoIssue {
  id          String   @id @default(uuid())
  pageId      String
  type        String   @db.VarChar(50)
  severity    String   @db.VarChar(20)
  description String
  status      String   @default("open") @db.VarChar(20)
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")
  page        SeoPage  @relation(fields: [pageId], references: [id], onDelete: Cascade)

  @@map("seo_issues")
}

model SeoKeyword {
  id          String   @id @default(uuid())
  pageId      String
  keyword     String   @db.VarChar(100)
  position    Int?
  volume      Int?
  difficulty  Int?
  lastChecked DateTime?
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")
  page        SeoPage  @relation(fields: [pageId], references: [id], onDelete: Cascade)

  @@map("seo_keywords")
}

model SeoMetaTag {
  id        String   @id @default(uuid())
  pageId    String
  name      String   @db.VarChar(100)
  content   String
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
  page      SeoPage  @relation(fields: [pageId], references: [id], onDelete: Cascade)

  @@map("seo_meta_tags")
}

model SeoStructuredData {
  id        String   @id @default(uuid())
  pageId    String
  type      String   @db.VarChar(50)
  data      Json
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
  page      SeoPage  @relation(fields: [pageId], references: [id], onDelete: Cascade)

  @@map("seo_structured_data")
}

model BrokenLink {
  id          String   @id @default(uuid())
  pageId      String
  url         String   @db.VarChar(255)
  statusCode  Int?
  description String?
  fixed       Boolean  @default(false)
  fixedUrl    String?  @db.VarChar(255)
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")
  page        SeoPage  @relation(fields: [pageId], references: [id], onDelete: Cascade)

  @@map("broken_links")
}

model SeoScan {
  id        String   @id @default(uuid())
  startedAt DateTime @default(now())
  endedAt   DateTime?
  status    String   @default("running") @db.VarChar(20)
  summary   Json?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("seo_scans")
}
